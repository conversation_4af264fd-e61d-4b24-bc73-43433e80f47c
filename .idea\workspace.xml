<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RunManager">
    <configuration name="GFDataProcessorJob" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.foshan.gf.GFDataProcessorJob" />
      <module name="gf-data-processor" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.foshan.gf.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.GFDataProcessorJob" />
      </list>
    </recent_temporary>
  </component>
</project>
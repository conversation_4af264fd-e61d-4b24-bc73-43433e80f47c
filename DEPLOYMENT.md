# 国发数据接入系统部署指南

## 🔧 Java 8 兼容性修复完成

已修复所有Java 8兼容性问题：
- ✅ 移除文本块（Text Blocks）语法，改为字符串拼接
- ✅ 更新Maven配置为Java 1.8
- ✅ 基础功能测试通过

## 📋 环境要求

### 必需环境
- **Java**: JDK 1.8+
- **Maven**: 3.6+
- **Apache Flink**: 1.17.2 (Standalone集群)
- **PostgreSQL**: 12+
- **Apache Kafka**: 2.8+

### 网络配置
- **Kafka集群**: ************:9296, ************:9296, ************:9296
- **PostgreSQL**: ************:5432
- **Flink Web UI**: http://localhost:8081

## 🚀 快速部署

### 1. 编译项目
```bash
# 进入项目目录
cd flink3

# 清理并编译
mvn clean compile

# 打包
mvn package -DskipTests
```

### 2. 验证编译结果
```bash
# 运行基础测试
javac -encoding UTF-8 -d target/classes src/main/java/com/foshan/gf/test/SimpleTest.java
java -cp target/classes com.foshan.gf.test.SimpleTest
```

预期输出：
```
=== GF Data Processor Test ===
1. Time Parsing Test:
  ************** -> 2025-07-30 14:00:00.0
  ...
3. Data Routing Test:
  Point ID: '11674' -> Route to: Main Table
  ...
=== Test Completed ===
```

### 3. 部署到Flink集群
```bash
# 提交作业到Flink
$FLINK_HOME/bin/flink run \
  --class com.foshan.gf.GFDataProcessorJob \
  --parallelism 2 \
  --detached \
  target/gf-data-processor-1.0.jar
```

### 4. 监控作业状态
```bash
# 查看作业列表
$FLINK_HOME/bin/flink list

# 查看作业详情
$FLINK_HOME/bin/flink info <job-id>

# 访问Web UI
# http://localhost:8081
```

## ⚙️ 配置说明

### 核心配置文件
`src/main/resources/application.properties`

```properties
# Flink应用配置
gf.app.parallelism=2

# Kafka配置
gf.kafka.bootstrap-servers=************:9296,************:9296,************:9296
gf.kafka.topic=t212_gf
gf.kafka.group-id=gf-data-processor

# PostgreSQL配置
gf.postgres.url=******************************************
gf.postgres.username=env_root
gf.postgres.password=env_root@fs_2020
gf.postgres.batch-size=2000
gf.postgres.batch-interval-ms=3000

# 分区管理配置
gf.partition.cache-size=1000
gf.partition.cache-expire-hours=1
```

## 📊 数据流架构

### 处理流程
```
Kafka Topic (t212_gf)
    ↓
JSON解析和验证
    ↓
第一级分流 (按cn字段)
├── 2051 (分钟数据)
├── 2061 (小时数据)  
└── 2031 (日数据)
    ↓
第二级分流 (按point_id)
├── point_id非空 → 正式表 (env_{data_type}_gf)
└── point_id为空 → 中间表 (env_{data_type}_gf_nomapping)
    ↓
分区检测和创建
    ↓
批量写入PostgreSQL (UPSERT策略)
```

### 分区策略
**正式表**:
- 一级分区：按`point_type`
- 二级分区：按时间
  - 2051: 按月 (202507)
  - 2061: 按年 (2025)
  - 2031: 不分区 (default)

**中间表**:
- 按月分区 (202507)

## 🔍 故障排查

### 常见问题

1. **编译错误 - 文本块语法**
   ```
   错误: -source 8 中不支持 文本块
   ```
   **解决**: 已修复，使用字符串拼接替代

2. **编码问题**
   ```
   错误: 编码 GBK 的不可映射字符
   ```
   **解决**: 使用 `-encoding UTF-8` 参数编译

3. **依赖缺失**
   ```
   错误: 找不到符号 Logger
   ```
   **解决**: 使用Maven编译，自动下载依赖

4. **Kafka连接失败**
   - 检查Kafka集群状态
   - 验证网络连通性
   - 确认topic存在

5. **PostgreSQL连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 确认用户权限

### 日志查看
```bash
# 查看应用日志
tail -f logs/gf-data-processor.log

# 查看Flink日志
tail -f $FLINK_HOME/log/flink-*-taskmanager-*.log
tail -f $FLINK_HOME/log/flink-*-jobmanager-*.log
```

## 📈 性能监控

### 关键指标
- **处理速度**: 目标 10,000条/秒
- **批量大小**: 2000条/批次
- **批量间隔**: 3秒
- **分区缓存**: 1000个分区，1小时过期

### 监控命令
```bash
# 查看作业指标
curl http://localhost:8081/jobs/<job-id>/metrics

# 查看检查点状态
curl http://localhost:8081/jobs/<job-id>/checkpoints
```

## 🛠️ 维护操作

### 停止作业
```bash
$FLINK_HOME/bin/flink cancel <job-id>
```

### 重启作业
```bash
# 从最新检查点恢复
$FLINK_HOME/bin/flink run \
  --fromSavepoint <savepoint-path> \
  --class com.foshan.gf.GFDataProcessorJob \
  --parallelism 2 \
  target/gf-data-processor-1.0.jar
```

### 扩缩容
```bash
# 修改并行度
$FLINK_HOME/bin/flink modify <job-id> --parallelism 4
```

## 📞 技术支持

如遇问题，请提供以下信息：
1. 错误日志
2. 作业配置
3. 环境信息
4. 数据样例

---
**版本**: v1.0  
**更新时间**: 2025-09-03  
**兼容性**: Java 8+, Flink 1.17.2

# 国发数据接入系统 (GF Data Processor)

基于Apache Flink的实时数据处理系统，用于接入佛山市局的国发数据。

## 系统概述

本系统从Kafka消费国发数据，经过实时处理后，根据业务规则分别写入PostgreSQL的不同分区表中。

### 核心功能

- **实时数据消费**: 从Kafka topic `t212_gf` 消费JSON格式的国发数据
- **二级数据分流**: 
  - 第一级：按`data_type`（cn字段）分离到不同表（2051/2061/2031）
  - 第二级：按`point_id`字段是否为空分离到正式表/中间表
- **动态分区管理**: 自动检测和创建PostgreSQL分区表
- **批量写入**: 支持UPSERT策略的批量数据写入
- **历史数据适配**: 无水位线模式，支持历史数据乱序处理

## 技术架构

- **Apache Flink**: 1.17.2 (流式处理引擎)
- **PostgreSQL**: 12 (数据存储)
- **Apache Kafka**: 2.8 (消息队列)
- **Java**: 8+ (开发语言)

## 项目结构

```
gf-data-processor/
├── src/main/java/com/foshan/gf/
│   ├── GFDataProcessorJob.java          # 主程序入口
│   ├── config/                          # 配置管理
│   │   ├── AppConfig.java
│   │   ├── KafkaConfig.java
│   │   └── PostgreSQLConfig.java
│   ├── model/                           # 数据模型
│   │   ├── GFDataRecord.java
│   │   ├── MainTableRecord.java
│   │   └── MidTableRecord.java
│   ├── source/                          # Source层
│   │   ├── GFKafkaSource.java
│   │   └── GFJsonDeserializer.java
│   ├── process/                         # Process层
│   │   ├── DataSplitProcessFunction.java
│   │   ├── PointIdSplitFunction.java
│   │   ├── MainTableMapper.java
│   │   └── MidTableMapper.java
│   ├── sink/                            # Sink层
│   │   ├── PostgreSQLSinkBuilder.java
│   │   ├── MainTableSinkFunction.java
│   │   └── MidTableSinkFunction.java
│   ├── partition/                       # 分区管理
│   │   └── PartitionManager.java
│   └── util/                            # 工具类
│       ├── DateTimeUtils.java
│       └── JsonUtils.java
├── src/main/resources/
│   ├── application.properties           # 应用配置
│   └── log4j2.properties               # 日志配置
└── pom.xml                             # Maven配置
```

## 配置说明

### Kafka配置
```properties
gf.kafka.bootstrap-servers=10.118.1.209:9296,10.118.1.210:9296,10.118.1.211:9296
gf.kafka.topic=t212_gf
gf.kafka.group-id=gf-data-processor
```

### PostgreSQL配置
```properties
gf.postgres.url=******************************************
gf.postgres.username=env_root
gf.postgres.password=env_root@fs_2020
gf.postgres.batch-size=2000
gf.postgres.batch-interval-ms=3000
```

## 数据流处理

### 数据分流规则

1. **第一级分流（按数据类型）**:
   - `cn="2051"` → 分钟数据流
   - `cn="2061"` → 小时数据流  
   - `cn="2031"` → 日数据流

2. **第二级分流（按站点标识）**:
   - `point_id`非空 → 正式表 (`env_{data_type}_gf`)
   - `point_id`为空 → 中间表 (`env_{data_type}_gf_nomapping`)

### 分区策略

**正式表分区**:
- 一级分区：按`point_type`进行LIST分区
- 二级分区：按`data_time`进行RANGE分区
  - 2051（分钟）：按月分区
  - 2061（小时）：按年分区
  - 2031（日）：不做时间分区

**中间表分区**:
- 单级分区：按`data_time`按月分区

## 编译和部署

### 1. 编译项目
```bash
mvn clean package
```

### 2. 提交Flink作业
```bash
# 提交到Flink集群
$FLINK_HOME/bin/flink run \
  --class com.foshan.gf.GFDataProcessorJob \
  --parallelism 2 \
  target/gf-data-processor-1.0.jar
```

### 3. 监控作业
```bash
# 查看作业状态
$FLINK_HOME/bin/flink list

# 查看作业详情
$FLINK_HOME/bin/flink info <job-id>
```

## 测试验证

### 基础功能测试
```bash
# 编译测试类
javac -d target/classes src/main/java/com/foshan/gf/test/SimpleTest.java

# 运行测试
java -cp target/classes com.foshan.gf.test.SimpleTest
```

### 测试数据样例
系统支持以下格式的JSON数据：
```json
{
  "id": "38a81b6672ae439781c3860be1e4aad0",
  "point_id": "11674",
  "point_type": "31", 
  "mn": "12315ctsy001",
  "cn": "2061",
  "data_time": "20250730140000",
  "factors": {"01": {"avg_value": "113.29"}},
  "gene_code_mapping": "{\"01\": \"NC147\"}"
}
```

## 性能指标

- **处理能力**: 每秒10,000条记录
- **并行度**: 2
- **批量大小**: 2000条/批次
- **批量间隔**: 3秒
- **分区缓存**: 1000个分区，1小时过期

## 监控和日志

- **日志文件**: `logs/gf-data-processor.log`
- **日志级别**: INFO（可配置）
- **监控指标**: 处理记录数、写入记录数、分区创建数、异常计数

## 故障处理

### 常见问题

1. **JSON解析失败**: 记录错误日志，跳过当前记录
2. **分区创建失败**: 重试3次，失败后记录错误
3. **数据库连接失败**: 自动重连，指数退避重试
4. **主键冲突**: 使用UPSERT策略自动处理

### 重启策略
- 最多重启3次
- 重启间隔30秒
- 固定延迟重启策略

## 版本信息

- **版本**: v1.0
- **创建日期**: 2025-08-28
- **最后更新**: 2025-09-03

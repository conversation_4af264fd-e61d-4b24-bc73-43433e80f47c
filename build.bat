@echo off
echo === 国发数据接入系统编译脚本 ===
echo.

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查Java环境
echo 1. 检查Java环境...
java -version
if %ERRORLEVEL% neq 0 (
    echo 错误: Java未安装或不在PATH中
    pause
    exit /b 1
)

REM 检查Maven环境
echo.
echo 2. 检查Maven环境...
mvn -version
if %ERRORLEVEL% neq 0 (
    echo 警告: Maven未安装，将使用手动编译
    goto MANUAL_COMPILE
)

REM Maven编译
echo.
echo 3. 使用Maven编译...
mvn clean compile
if %ERRORLEVEL% neq 0 (
    echo Maven编译失败，尝试手动编译...
    goto MANUAL_COMPILE
)

echo.
echo 4. Maven打包...
mvn package -DskipTests
if %ERRORLEVEL% neq 0 (
    echo Maven打包失败
    goto MANUAL_COMPILE
)

echo.
echo ✓ Maven编译打包成功！
echo JAR文件: target\gf-data-processor-1.0.jar
goto TEST

:MANUAL_COMPILE
echo.
echo 3. 手动编译基础测试...
if not exist "target\classes" mkdir target\classes

javac -encoding UTF-8 -d target\classes src\main\java\com\foshan\gf\test\SimpleTest.java
if %ERRORLEVEL% neq 0 (
    echo 手动编译失败
    pause
    exit /b 1
)

echo ✓ 手动编译成功！

:TEST
echo.
echo 4. 运行基础测试...
java -cp target\classes com.foshan.gf.test.SimpleTest
if %ERRORLEVEL% neq 0 (
    echo 测试失败
    pause
    exit /b 1
)

echo.
echo ✓ 基础测试通过！

:END
echo.
echo === 编译完成 ===
echo.
echo 下一步操作:
if exist "target\gf-data-processor-1.0.jar" (
    echo 1. 部署到Flink集群:
    echo    %%FLINK_HOME%%\bin\flink run --class com.foshan.gf.GFDataProcessorJob --parallelism 2 target\gf-data-processor-1.0.jar
) else (
    echo 1. 安装Maven后重新编译以生成完整JAR包
    echo 2. 或者手动配置classpath运行程序
)
echo.
echo 2. 查看部署文档: DEPLOYMENT.md
echo 3. 查看项目说明: README.md
echo.
pause

@echo off
echo === 编译和测试国发数据处理程序 ===

REM 创建lib目录
if not exist "lib" mkdir lib

REM 创建编译输出目录
if not exist "target\classes" mkdir target\classes

echo 1. 下载必要的JAR包...
REM 这里需要手动下载JAR包到lib目录
REM jackson-databind-2.12.6.jar
REM jackson-core-2.12.6.jar
REM jackson-annotations-2.12.6.jar
REM guava-31.1-jre.jar
REM slf4j-api-1.7.36.jar
REM slf4j-simple-1.7.36.jar

echo 2. 编译Java源码...
REM 设置classpath
set CLASSPATH=lib\*;target\classes

REM 编译工具类
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\foshan\gf\util\*.java
if %ERRORLEVEL% neq 0 (
    echo 编译工具类失败
    pause
    exit /b 1
)

REM 编译配置类
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\foshan\gf\config\*.java
if %ERRORLEVEL% neq 0 (
    echo 编译配置类失败
    pause
    exit /b 1
)

REM 编译模型类
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\foshan\gf\model\*.java
if %ERRORLEVEL% neq 0 (
    echo 编译模型类失败
    pause
    exit /b 1
)

REM 编译测试类
javac -cp "%CLASSPATH%" -d target\classes src\main\java\com\foshan\gf\test\*.java
if %ERRORLEVEL% neq 0 (
    echo 编译测试类失败
    pause
    exit /b 1
)

echo 3. 运行JSON解析测试...
java -cp "%CLASSPATH%" com.foshan.gf.test.SimpleJsonTest

echo === 编译和测试完成 ===
pause

2025-09-03 15:50:35,133 INFO  [main] GFDataProcessorJob:36 - Starting GF Data Processor Job - Version: 1.0
2025-09-03 15:50:35,202 INFO  [main] GFDataProcessorJob:58 - Set parallelism to: 2
2025-09-03 15:50:35,203 INFO  [main] GFDataProcessorJob:75 - Configured Flink execution environment
2025-09-03 15:50:35,205 INFO  [main] GFKafkaSource:32 - Creating Kafka source with config: bootstrap-servers=10.118.1.209:9296,10.118.1.210:9296,10.118.1.211:9296, topic=t212_gf, group-id=gf-data-processor
2025-09-03 15:50:35,209 INFO  [main] GFKafkaSource:76 - Using latest offset initializer
2025-09-03 15:50:35,213 WARN  [main] KafkaSourceBuilder:481 - Property key.deserializer is provided but will be overridden from org.apache.kafka.common.serialization.StringDeserializer to org.apache.kafka.common.serialization.ByteArrayDeserializer
2025-09-03 15:50:35,213 WARN  [main] KafkaSourceBuilder:481 - Property value.deserializer is provided but will be overridden from org.apache.kafka.common.serialization.StringDeserializer to org.apache.kafka.common.serialization.ByteArrayDeserializer
2025-09-03 15:50:35,213 WARN  [main] KafkaSourceBuilder:481 - Property auto.offset.reset is provided but will be overridden from latest to latest
2025-09-03 15:50:35,256 INFO  [main] GFKafkaSource:59 - Created GF data stream from Kafka source
2025-09-03 15:50:35,256 INFO  [main] GFDataProcessorJob:87 - Created Kafka source stream
2025-09-03 15:50:35,305 INFO  [main] GFDataProcessorJob:103 - Created data type split streams
2025-09-03 15:51:17,798 INFO  [main] GFDataProcessorJob:36 - Starting GF Data Processor Job - Version: 1.0
2025-09-03 15:51:17,877 INFO  [main] GFDataProcessorJob:58 - Set parallelism to: 2
2025-09-03 15:51:17,878 INFO  [main] GFDataProcessorJob:75 - Configured Flink execution environment
2025-09-03 15:51:17,879 INFO  [main] GFKafkaSource:32 - Creating Kafka source with config: bootstrap-servers=10.118.1.209:9296,10.118.1.210:9296,10.118.1.211:9296, topic=t212_gf, group-id=gf-data-processor
2025-09-03 15:51:17,887 INFO  [main] GFKafkaSource:76 - Using latest offset initializer
2025-09-03 15:51:17,890 WARN  [main] KafkaSourceBuilder:481 - Property key.deserializer is provided but will be overridden from org.apache.kafka.common.serialization.StringDeserializer to org.apache.kafka.common.serialization.ByteArrayDeserializer
2025-09-03 15:51:17,890 WARN  [main] KafkaSourceBuilder:481 - Property value.deserializer is provided but will be overridden from org.apache.kafka.common.serialization.StringDeserializer to org.apache.kafka.common.serialization.ByteArrayDeserializer
2025-09-03 15:51:17,890 WARN  [main] KafkaSourceBuilder:481 - Property auto.offset.reset is provided but will be overridden from latest to latest
2025-09-03 15:51:17,934 INFO  [main] GFKafkaSource:59 - Created GF data stream from Kafka source
2025-09-03 15:51:17,934 INFO  [main] GFDataProcessorJob:87 - Created Kafka source stream
2025-09-03 15:51:17,983 INFO  [main] GFDataProcessorJob:103 - Created data type split streams

#!/bin/bash

# 国发数据接入系统部署脚本
# 用于编译、打包和部署Flink作业

set -e

echo "=== 国发数据接入系统部署脚本 ==="

# 配置变量
PROJECT_NAME="gf-data-processor"
VERSION="1.0"
JAR_NAME="${PROJECT_NAME}-${VERSION}.jar"
FLINK_HOME=${FLINK_HOME:-"/opt/flink"}
PARALLELISM=${PARALLELISM:-2}

# 检查环境
check_environment() {
    echo "1. 检查环境..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        echo "错误: Java未安装或不在PATH中"
        exit 1
    fi
    
    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    echo "  Java版本: $java_version"
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        echo "错误: Maven未安装或不在PATH中"
        exit 1
    fi
    
    mvn_version=$(mvn -version | head -n 1)
    echo "  Maven版本: $mvn_version"
    
    # 检查Flink
    if [ ! -d "$FLINK_HOME" ]; then
        echo "警告: FLINK_HOME未设置或目录不存在: $FLINK_HOME"
        echo "  请设置正确的FLINK_HOME环境变量"
    else
        echo "  Flink目录: $FLINK_HOME"
    fi
}

# 编译项目
compile_project() {
    echo "2. 编译项目..."
    
    # 清理和编译
    mvn clean compile
    
    if [ $? -eq 0 ]; then
        echo "  ✓ 编译成功"
    else
        echo "  ✗ 编译失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    echo "3. 运行测试..."
    
    # 编译测试类
    javac -d target/classes src/main/java/com/foshan/gf/test/SimpleTest.java
    
    # 运行基础测试
    java -cp target/classes com.foshan.gf.test.SimpleTest
    
    if [ $? -eq 0 ]; then
        echo "  ✓ 测试通过"
    else
        echo "  ✗ 测试失败"
        exit 1
    fi
}

# 打包项目
package_project() {
    echo "4. 打包项目..."
    
    mvn package -DskipTests
    
    if [ $? -eq 0 ] && [ -f "target/$JAR_NAME" ]; then
        echo "  ✓ 打包成功: target/$JAR_NAME"
        
        # 显示JAR文件信息
        jar_size=$(du -h "target/$JAR_NAME" | cut -f1)
        echo "  JAR文件大小: $jar_size"
    else
        echo "  ✗ 打包失败"
        exit 1
    fi
}

# 部署到Flink
deploy_to_flink() {
    echo "5. 部署到Flink..."
    
    if [ ! -d "$FLINK_HOME" ]; then
        echo "  跳过Flink部署 (FLINK_HOME未设置)"
        return
    fi
    
    # 检查Flink是否运行
    if ! $FLINK_HOME/bin/flink list &> /dev/null; then
        echo "  警告: Flink集群可能未运行"
        echo "  请先启动Flink集群: $FLINK_HOME/bin/start-cluster.sh"
        return
    fi
    
    # 停止现有作业（如果存在）
    existing_job=$($FLINK_HOME/bin/flink list | grep "$PROJECT_NAME" | awk '{print $4}' | head -n 1)
    if [ ! -z "$existing_job" ]; then
        echo "  停止现有作业: $existing_job"
        $FLINK_HOME/bin/flink cancel $existing_job
        sleep 5
    fi
    
    # 提交新作业
    echo "  提交Flink作业..."
    $FLINK_HOME/bin/flink run \
        --class com.foshan.gf.GFDataProcessorJob \
        --parallelism $PARALLELISM \
        --detached \
        target/$JAR_NAME
    
    if [ $? -eq 0 ]; then
        echo "  ✓ 作业提交成功"
        
        # 显示作业状态
        sleep 3
        echo "  当前作业状态:"
        $FLINK_HOME/bin/flink list
    else
        echo "  ✗ 作业提交失败"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo "6. 部署信息..."
    
    echo "  项目名称: $PROJECT_NAME"
    echo "  版本: $VERSION"
    echo "  JAR文件: target/$JAR_NAME"
    echo "  并行度: $PARALLELISM"
    echo "  Flink目录: $FLINK_HOME"
    
    if [ -d "$FLINK_HOME" ]; then
        echo ""
        echo "  监控命令:"
        echo "    查看作业列表: $FLINK_HOME/bin/flink list"
        echo "    查看作业详情: $FLINK_HOME/bin/flink info <job-id>"
        echo "    取消作业: $FLINK_HOME/bin/flink cancel <job-id>"
        echo "    Web UI: http://localhost:8081"
    fi
    
    echo ""
    echo "  日志文件: logs/gf-data-processor.log"
    echo "  配置文件: src/main/resources/application.properties"
}

# 主函数
main() {
    check_environment
    compile_project
    run_tests
    package_project
    deploy_to_flink
    show_deployment_info
    
    echo ""
    echo "=== 部署完成 ==="
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --parallelism)
            PARALLELISM="$2"
            shift 2
            ;;
        --flink-home)
            FLINK_HOME="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --parallelism <num>    设置并行度 (默认: 2)"
            echo "  --flink-home <path>    设置Flink安装目录"
            echo "  --skip-tests           跳过测试"
            echo "  --help                 显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 执行主函数
main

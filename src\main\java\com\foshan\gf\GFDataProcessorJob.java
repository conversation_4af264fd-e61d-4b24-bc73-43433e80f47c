package com.foshan.gf;

import com.foshan.gf.config.AppConfig;
import com.foshan.gf.model.GFDataRecord;
import com.foshan.gf.model.MainTableRecord;
import com.foshan.gf.model.MidTableRecord;
import com.foshan.gf.process.DataSplitProcessFunction;
import com.foshan.gf.process.MainTableMapper;
import com.foshan.gf.process.MidTableMapper;
import com.foshan.gf.process.PointIdSplitFunction;
import com.foshan.gf.sink.MainTableSinkFunction;
import com.foshan.gf.sink.MidTableSinkFunction;
import com.foshan.gf.source.GFKafkaSource;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 国发数据处理作业主程序
 * 整合所有模块，构建完整的数据处理流水线
 */
public class GFDataProcessorJob {
    
    private static final Logger log = LoggerFactory.getLogger(GFDataProcessorJob.class);
    
    public static void main(String[] args) throws Exception {
        // 加载配置
        AppConfig config = AppConfig.getInstance();
        
        log.info("Starting GF Data Processor Job - Version: {}", config.getAppVersion());
        
        // 创建执行环境
        StreamExecutionEnvironment env = createExecutionEnvironment(config);
        
        // 构建数据处理流水线
        buildDataPipeline(env);
        
        // 执行作业
        String jobName = config.getAppName() + "-v" + config.getAppVersion();
        log.info("Executing job: {}", jobName);
        env.execute(jobName);
    }
    
    /**
     * 创建和配置Flink执行环境
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(AppConfig config) {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(config.getAppParallelism());
        log.info("Set parallelism to: {}", config.getAppParallelism());
        
        // 配置检查点
        env.enableCheckpointing(60000); // 1分钟检查点间隔
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(30000); // 30秒最小间隔
        env.getCheckpointConfig().setCheckpointTimeout(300000); // 5分钟超时
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        env.getCheckpointConfig().setExternalizedCheckpointCleanup(
            CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        
        // 配置重启策略
        env.setRestartStrategy(RestartStrategies.fixedDelayRestart(
            3, // 最多重启3次
            Time.seconds(30) // 重启间隔30秒
        ));
        
        log.info("Configured Flink execution environment");
        return env;
    }
    
    /**
     * 构建数据处理流水线
     */
    private static void buildDataPipeline(StreamExecutionEnvironment env) {
        // 1. 创建Kafka数据源
        GFKafkaSource kafkaSource = new GFKafkaSource();
        DataStream<GFDataRecord> sourceStream = kafkaSource.createDataStream(env);
        
        log.info("Created Kafka source stream");
        
        // 2. 第一级分流：按数据类型分离
        SingleOutputStreamOperator<GFDataRecord> mainStream = sourceStream
                .process(new DataSplitProcessFunction())
                .name("Data-Type-Split")
                .uid("data-type-split");
        
        // 获取各数据类型的流
        DataStream<GFDataRecord> minuteDataStream = mainStream
                .getSideOutput(DataSplitProcessFunction.getMinuteDataTag());
        DataStream<GFDataRecord> hourDataStream = mainStream
                .getSideOutput(DataSplitProcessFunction.getHourDataTag());
        DataStream<GFDataRecord> dayDataStream = mainStream
                .getSideOutput(DataSplitProcessFunction.getDayDataTag());
        
        log.info("Created data type split streams");
        
        // 3. 处理各数据类型流
        processDataTypeStream(minuteDataStream, "2051");
        processDataTypeStream(hourDataStream, "2061");
        processDataTypeStream(dayDataStream, "2031");
        
        log.info("Configured all data type processing streams");
    }
    
    /**
     * 处理特定数据类型的流
     */
    private static void processDataTypeStream(DataStream<GFDataRecord> dataStream, String dataType) {
        // 第二级分流：按point_id分离
        SingleOutputStreamOperator<GFDataRecord> mainTableStream = dataStream
                .process(new PointIdSplitFunction(dataType))
                .name("Point-ID-Split-" + dataType)
                .uid("point-id-split-" + dataType);
        
        // 获取中间表流
        DataStream<GFDataRecord> midTableStream = mainTableStream
                .getSideOutput(PointIdSplitFunction.getMidTableTag());
        
        // 处理正式表流
        DataStream<MainTableRecord> mainRecordStream = mainTableStream
                .map(new MainTableMapper())
                .name("Main-Table-Mapper-" + dataType)
                .uid("main-table-mapper-" + dataType);
        
        // 处理中间表流
        DataStream<MidTableRecord> midRecordStream = midTableStream
                .map(new MidTableMapper())
                .name("Mid-Table-Mapper-" + dataType)
                .uid("mid-table-mapper-" + dataType);
        
        // 写入正式表
        mainRecordStream
                .addSink(new MainTableSinkFunction(dataType))
                .name("Main-Table-Sink-" + dataType)
                .uid("main-table-sink-" + dataType);
        
        // 写入中间表
        midRecordStream
                .addSink(new MidTableSinkFunction(dataType))
                .name("Mid-Table-Sink-" + dataType)
                .uid("mid-table-sink-" + dataType);
        
        log.info("Configured processing pipeline for data type: {}", dataType);
    }
}

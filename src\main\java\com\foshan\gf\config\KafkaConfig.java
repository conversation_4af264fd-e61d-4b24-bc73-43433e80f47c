package com.foshan.gf.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.Properties;

/**
 * Kafka配置类
 * 管理Kafka消费者相关配置
 */
public class KafkaConfig {
    
    private final AppConfig appConfig;
    
    public KafkaConfig() {
        this.appConfig = AppConfig.getInstance();
    }
    
    public String getBootstrapServers() {
        return appConfig.getString("gf.kafka.bootstrap-servers", "localhost:9092");
    }
    
    public String getTopic() {
        return appConfig.getString("gf.kafka.topic", "t212_gf");
    }
    
    public String getGroupId() {
        return appConfig.getString("gf.kafka.group-id", "gf-data-processor");
    }
    
    public int getMaxPollRecords() {
        return appConfig.getInt("gf.kafka.max-poll-records", 5000);
    }
    
    public int getFetchMaxBytes() {
        return appConfig.getInt("gf.kafka.fetch-max-bytes", 52428800);
    }
    
    public String getAutoOffsetReset() {
        return appConfig.getString("gf.kafka.auto-offset-reset", "latest");
    }
    
    public boolean getEnableAutoCommit() {
        return appConfig.getBoolean("gf.kafka.enable-auto-commit", false);
    }
    
    public String getIsolationLevel() {
        return appConfig.getString("gf.kafka.isolation-level", "read_committed");
    }
    
    /**
     * 构建Kafka消费者Properties
     */
    public Properties getConsumerProperties() {
        Properties props = new Properties();
        
        // 基础配置
        props.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, getBootstrapServers());
        props.setProperty(ConsumerConfig.GROUP_ID_CONFIG, getGroupId());
        props.setProperty(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.setProperty(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        
        // 性能优化配置
        props.setProperty(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, String.valueOf(getMaxPollRecords()));
        props.setProperty(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, String.valueOf(getFetchMaxBytes()));
        props.setProperty(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, "1024");
        props.setProperty(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, "100");
        props.setProperty(ConsumerConfig.RECEIVE_BUFFER_CONFIG, "65536");
        
        // 可靠性配置
        props.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, getAutoOffsetReset());
        props.setProperty(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, String.valueOf(getEnableAutoCommit()));
        props.setProperty(ConsumerConfig.ISOLATION_LEVEL_CONFIG, getIsolationLevel());
        
        return props;
    }
}

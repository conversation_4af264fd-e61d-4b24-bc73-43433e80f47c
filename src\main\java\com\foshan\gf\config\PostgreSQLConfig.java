package com.foshan.gf.config;

import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;

/**
 * PostgreSQL配置类
 * 管理数据库连接和执行相关配置
 */
public class PostgreSQLConfig {
    
    private final AppConfig appConfig;
    
    public PostgreSQLConfig() {
        this.appConfig = AppConfig.getInstance();
    }
    
    public String getUrl() {
        return appConfig.getString("gf.postgres.url", "****************************************");
    }
    
    public String getUsername() {
        return appConfig.getString("gf.postgres.username", "flink_user");
    }
    
    public String getPassword() {
        return appConfig.getString("gf.postgres.password", "password");
    }
    
    public int getBatchSize() {
        return appConfig.getInt("gf.postgres.batch-size", 2000);
    }
    
    public long getBatchIntervalMs() {
        return appConfig.getLong("gf.postgres.batch-interval-ms", 3000L);
    }
    
    public int getMaxRetries() {
        return appConfig.getInt("gf.postgres.max-retries", 3);
    }
    
    public int getConnectionTimeout() {
        return appConfig.getInt("gf.postgres.connection-timeout", 30000);
    }
    
    /**
     * 构建JDBC连接选项
     */
    public JdbcConnectionOptions getConnectionOptions() {
        return new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                .withUrl(getUrl())
                .withDriverName("org.postgresql.Driver")
                .withUsername(getUsername())
                .withPassword(getPassword())
                .withConnectionCheckTimeoutSeconds(getConnectionTimeout() / 1000)
                .build();
    }
    
    /**
     * 构建JDBC执行选项
     */
    public JdbcExecutionOptions getExecutionOptions() {
        return JdbcExecutionOptions.builder()
                .withBatchSize(getBatchSize())
                .withBatchIntervalMs(getBatchIntervalMs())
                .withMaxRetries(getMaxRetries())
                .build();
    }
    
    /**
     * 获取分区缓存大小
     */
    public int getPartitionCacheSize() {
        return appConfig.getInt("gf.partition.cache-size", 1000);
    }
    
    /**
     * 获取分区缓存过期时间（小时）
     */
    public int getPartitionCacheExpireHours() {
        return appConfig.getInt("gf.partition.cache-expire-hours", 1);
    }
    
    /**
     * 获取分区检查间隔（秒）
     */
    public int getPartitionCheckIntervalSeconds() {
        return appConfig.getInt("gf.partition.check-interval-seconds", 300);
    }
}

package com.foshan.gf.exception;

/**
 * 分区管理异常类
 * 用于分区检测、创建等操作的异常处理
 */
public class PartitionException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    public PartitionException(String message) {
        super(message);
    }
    
    public PartitionException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public PartitionException(Throwable cause) {
        super(cause);
    }
}

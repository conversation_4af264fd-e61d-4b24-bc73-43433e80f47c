package com.foshan.gf.model;

import com.fasterxml.jackson.databind.JsonNode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 国发数据记录模型
 * 对应Kafka中接收到的原始JSON数据结构
 */
public class GFDataRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 基础字段
    private String id;                      // 唯一标识
    private String pointId;                 // 站点ID（可为空）
    private String pointType;               // 站点类型
    private String operationCustomerId;     // 运维企业ID
    private String customerId;              // 客户编码
    private String mn;                      // 设备MN码
    private String cn;                      // 数据类型（2051/2061/2031）
    private String dataTime;                // 数据时间字符串（yyyyMMddHHmmss）
    private String geneCodeMapping;         // 因子编码映射JSON字符串
    private JsonNode factors;               // 因子监测数据JSON
    
    // 其他字段
    private String pwd;                     // 密码
    private String outletCode;              // 排放口编码
    private String workcordSc;              // 工况数据
    private String zdWorkcordSc;            // 质控工况数据
    private String zdWorkcordZl;            // 质控质量数据
    private String workcordZl;              // 工况质量数据
    private String rgWorkcordSc;            // 人工工况数据
    private String rgWorkcordZl;            // 人工质量数据
    private String logTime;                 // 日志时间
    
    // 处理后的字段
    private Timestamp parsedDataTime;       // 解析后的时间戳
    private String partitionKey;            // 分区键
    private String rawJson;                 // 原始JSON字符串
    
    // 构造函数
    public GFDataRecord() {}
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    public String getPointType() {
        return pointType;
    }
    
    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    
    public String getOperationCustomerId() {
        return operationCustomerId;
    }
    
    public void setOperationCustomerId(String operationCustomerId) {
        this.operationCustomerId = operationCustomerId;
    }
    
    public String getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    
    public String getMn() {
        return mn;
    }
    
    public void setMn(String mn) {
        this.mn = mn;
    }
    
    public String getCn() {
        return cn;
    }
    
    public void setCn(String cn) {
        this.cn = cn;
    }
    
    public String getDataTime() {
        return dataTime;
    }
    
    public void setDataTime(String dataTime) {
        this.dataTime = dataTime;
    }
    
    public String getGeneCodeMapping() {
        return geneCodeMapping;
    }
    
    public void setGeneCodeMapping(String geneCodeMapping) {
        this.geneCodeMapping = geneCodeMapping;
    }
    
    public JsonNode getFactors() {
        return factors;
    }
    
    public void setFactors(JsonNode factors) {
        this.factors = factors;
    }
    
    public String getPwd() {
        return pwd;
    }
    
    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
    
    public String getOutletCode() {
        return outletCode;
    }
    
    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }
    
    public String getWorkcordSc() {
        return workcordSc;
    }
    
    public void setWorkcordSc(String workcordSc) {
        this.workcordSc = workcordSc;
    }
    
    public String getZdWorkcordSc() {
        return zdWorkcordSc;
    }
    
    public void setZdWorkcordSc(String zdWorkcordSc) {
        this.zdWorkcordSc = zdWorkcordSc;
    }
    
    public String getZdWorkcordZl() {
        return zdWorkcordZl;
    }
    
    public void setZdWorkcordZl(String zdWorkcordZl) {
        this.zdWorkcordZl = zdWorkcordZl;
    }
    
    public String getWorkcordZl() {
        return workcordZl;
    }
    
    public void setWorkcordZl(String workcordZl) {
        this.workcordZl = workcordZl;
    }
    
    public String getRgWorkcordSc() {
        return rgWorkcordSc;
    }
    
    public void setRgWorkcordSc(String rgWorkcordSc) {
        this.rgWorkcordSc = rgWorkcordSc;
    }
    
    public String getRgWorkcordZl() {
        return rgWorkcordZl;
    }
    
    public void setRgWorkcordZl(String rgWorkcordZl) {
        this.rgWorkcordZl = rgWorkcordZl;
    }
    
    public String getLogTime() {
        return logTime;
    }
    
    public void setLogTime(String logTime) {
        this.logTime = logTime;
    }
    
    public Timestamp getParsedDataTime() {
        return parsedDataTime;
    }
    
    public void setParsedDataTime(Timestamp parsedDataTime) {
        this.parsedDataTime = parsedDataTime;
    }
    
    public String getPartitionKey() {
        return partitionKey;
    }
    
    public void setPartitionKey(String partitionKey) {
        this.partitionKey = partitionKey;
    }
    
    public String getRawJson() {
        return rawJson;
    }
    
    public void setRawJson(String rawJson) {
        this.rawJson = rawJson;
    }
    
    /**
     * 判断是否有point_id（用于分流判断）
     */
    public boolean hasPointId() {
        return pointId != null && !pointId.trim().isEmpty();
    }
    
    /**
     * 获取数据类型（cn字段）
     */
    public String getDataType() {
        return cn;
    }
    
    @Override
    public String toString() {
        return "GFDataRecord{" +
                "id='" + id + '\'' +
                ", pointId='" + pointId + '\'' +
                ", pointType='" + pointType + '\'' +
                ", mn='" + mn + '\'' +
                ", cn='" + cn + '\'' +
                ", dataTime='" + dataTime + '\'' +
                ", parsedDataTime=" + parsedDataTime +
                '}';
    }
}

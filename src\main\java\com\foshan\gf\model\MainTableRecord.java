package com.foshan.gf.model;

import com.fasterxml.jackson.databind.JsonNode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 正式表记录模型
 * 对应env_{data_type}_gf表结构（point_id非空的数据）
 */
public class MainTableRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 主键字段
    private Integer pointId;                // 站点ID
    private Timestamp dataTime;             // 数据时间
    private String operationCustomerId;     // 运维企业ID
    private String dataType;                // 数据类型
    private String source;                  // 数据来源标识
    
    // 数据字段
    private String qn;                      // 请求编码
    private String st;                      // 系统编码
    private String mncode;                  // 设备唯一标识
    private String flag;                    // 拆分包及应答标志
    private JsonNode cpExt;                 // 指令参数CP中的非因子监测数据
    private JsonNode geneJson;              // 因子监测信息
    private JsonNode mainExt;               // 数据段中非固定字段的数据
    private JsonNode autoAudit;             // 自动审核质控数据
    private JsonNode extProperty;           // 报文附加属性
    private String pointType;               // 站点类型
    private Timestamp writeTime;            // 报文写入时间
    private JsonNode geneJsonStd;           // 标准化因子数据
    private JsonNode waterGeneLabel;        // 水质因子标签
    private JsonNode waterLabel;            // 水质报文标签
    private JsonNode commRule;              // 通用异常标签
    private JsonNode labelJson;             // 标签JSON
    private JsonNode geneTransJson;         // 因子编码转换JSON
    
    // 构造函数
    public MainTableRecord() {
        this.source = "nt";  // 默认来源标识
        this.writeTime = new Timestamp(System.currentTimeMillis());
    }
    
    // Getter和Setter方法
    public Integer getPointId() {
        return pointId;
    }
    
    public void setPointId(Integer pointId) {
        this.pointId = pointId;
    }
    
    public Timestamp getDataTime() {
        return dataTime;
    }
    
    public void setDataTime(Timestamp dataTime) {
        this.dataTime = dataTime;
    }
    
    public String getOperationCustomerId() {
        return operationCustomerId;
    }
    
    public void setOperationCustomerId(String operationCustomerId) {
        this.operationCustomerId = operationCustomerId;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getQn() {
        return qn;
    }
    
    public void setQn(String qn) {
        this.qn = qn;
    }
    
    public String getSt() {
        return st;
    }
    
    public void setSt(String st) {
        this.st = st;
    }
    
    public String getMncode() {
        return mncode;
    }
    
    public void setMncode(String mncode) {
        this.mncode = mncode;
    }
    
    public String getFlag() {
        return flag;
    }
    
    public void setFlag(String flag) {
        this.flag = flag;
    }
    
    public JsonNode getCpExt() {
        return cpExt;
    }
    
    public void setCpExt(JsonNode cpExt) {
        this.cpExt = cpExt;
    }
    
    public JsonNode getGeneJson() {
        return geneJson;
    }
    
    public void setGeneJson(JsonNode geneJson) {
        this.geneJson = geneJson;
    }
    
    public JsonNode getMainExt() {
        return mainExt;
    }
    
    public void setMainExt(JsonNode mainExt) {
        this.mainExt = mainExt;
    }
    
    public JsonNode getAutoAudit() {
        return autoAudit;
    }
    
    public void setAutoAudit(JsonNode autoAudit) {
        this.autoAudit = autoAudit;
    }
    
    public JsonNode getExtProperty() {
        return extProperty;
    }
    
    public void setExtProperty(JsonNode extProperty) {
        this.extProperty = extProperty;
    }
    
    public String getPointType() {
        return pointType;
    }
    
    public void setPointType(String pointType) {
        this.pointType = pointType;
    }
    
    public Timestamp getWriteTime() {
        return writeTime;
    }
    
    public void setWriteTime(Timestamp writeTime) {
        this.writeTime = writeTime;
    }
    
    public JsonNode getGeneJsonStd() {
        return geneJsonStd;
    }
    
    public void setGeneJsonStd(JsonNode geneJsonStd) {
        this.geneJsonStd = geneJsonStd;
    }
    
    public JsonNode getWaterGeneLabel() {
        return waterGeneLabel;
    }
    
    public void setWaterGeneLabel(JsonNode waterGeneLabel) {
        this.waterGeneLabel = waterGeneLabel;
    }
    
    public JsonNode getWaterLabel() {
        return waterLabel;
    }
    
    public void setWaterLabel(JsonNode waterLabel) {
        this.waterLabel = waterLabel;
    }
    
    public JsonNode getCommRule() {
        return commRule;
    }
    
    public void setCommRule(JsonNode commRule) {
        this.commRule = commRule;
    }
    
    public JsonNode getLabelJson() {
        return labelJson;
    }
    
    public void setLabelJson(JsonNode labelJson) {
        this.labelJson = labelJson;
    }
    
    public JsonNode getGeneTransJson() {
        return geneTransJson;
    }
    
    public void setGeneTransJson(JsonNode geneTransJson) {
        this.geneTransJson = geneTransJson;
    }
    
    @Override
    public String toString() {
        return "MainTableRecord{" +
                "pointId=" + pointId +
                ", dataTime=" + dataTime +
                ", dataType='" + dataType + '\'' +
                ", qn='" + qn + '\'' +
                ", mncode='" + mncode + '\'' +
                ", pointType='" + pointType + '\'' +
                ", source='" + source + '\'' +
                '}';
    }
}

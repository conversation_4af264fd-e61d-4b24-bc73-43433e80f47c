package com.foshan.gf.model;

import com.fasterxml.jackson.databind.JsonNode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 中间表记录模型
 * 对应env_{data_type}_gf_nomapping表结构（point_id为空的数据）
 */
public class MidTableRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // 主键字段
    private String customerId;              // 客户编码
    private String mncode;                  // 设备唯一标识
    private Timestamp dataTime;             // 数据时间
    
    // 数据字段
    private String dataType;                // 数据类型
    private String qn;                      // 请求编码
    private JsonNode geneJson;              // 因子监测信息
    private JsonNode mainExt;               // 数据段中非固定字段的数据
    private Timestamp writeTime;            // 报文写入时间
    
    // 构造函数
    public MidTableRecord() {
        this.writeTime = new Timestamp(System.currentTimeMillis());
    }
    
    // Getter和Setter方法
    public String getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
    
    public String getMncode() {
        return mncode;
    }
    
    public void setMncode(String mncode) {
        this.mncode = mncode;
    }
    
    public Timestamp getDataTime() {
        return dataTime;
    }
    
    public void setDataTime(Timestamp dataTime) {
        this.dataTime = dataTime;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public String getQn() {
        return qn;
    }
    
    public void setQn(String qn) {
        this.qn = qn;
    }
    
    public JsonNode getGeneJson() {
        return geneJson;
    }
    
    public void setGeneJson(JsonNode geneJson) {
        this.geneJson = geneJson;
    }
    
    public JsonNode getMainExt() {
        return mainExt;
    }
    
    public void setMainExt(JsonNode mainExt) {
        this.mainExt = mainExt;
    }
    
    public Timestamp getWriteTime() {
        return writeTime;
    }
    
    public void setWriteTime(Timestamp writeTime) {
        this.writeTime = writeTime;
    }
    
    @Override
    public String toString() {
        return "MidTableRecord{" +
                "customerId='" + customerId + '\'' +
                ", mncode='" + mncode + '\'' +
                ", dataTime=" + dataTime +
                ", dataType='" + dataType + '\'' +
                ", qn='" + qn + '\'' +
                '}';
    }
}

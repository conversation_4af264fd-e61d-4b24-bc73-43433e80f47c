package com.foshan.gf.partition;

import com.foshan.gf.config.PostgreSQLConfig;
import com.foshan.gf.exception.PartitionException;
import com.foshan.gf.util.DateTimeUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 分区管理器
 * 负责分区的检测、创建和缓存管理
 */
public class PartitionManager {
    
    private static final Logger log = LoggerFactory.getLogger(PartitionManager.class);
    
    private final Connection connection;
    private final Cache<String, Boolean> partitionCache;
    private final PostgreSQLConfig config;
    
    public PartitionManager(Connection connection) {
        this.connection = connection;
        this.config = new PostgreSQLConfig();
        
        // 初始化分区缓存
        this.partitionCache = CacheBuilder.newBuilder()
                .maximumSize(config.getPartitionCacheSize())
                .expireAfterWrite(config.getPartitionCacheExpireHours(), TimeUnit.HOURS)
                .build();
        
        log.info("Initialized PartitionManager with cache size: {}, expire hours: {}", 
            config.getPartitionCacheSize(), config.getPartitionCacheExpireHours());
    }
    
    /**
     * 确保分区存在，如果不存在则创建
     * @param dataType 数据类型（2051/2061/2031）
     * @param pointType 站点类型
     * @param dataTime 数据时间
     * @param isMainTable 是否为正式表
     * @return true如果分区存在或创建成功
     */
    public boolean ensurePartitionExists(String dataType, String pointType, 
                                       Timestamp dataTime, boolean isMainTable) {
        try {
            String partitionName = calculatePartitionName(dataType, pointType, dataTime, isMainTable);
            
            // 先查缓存
            Boolean exists = partitionCache.getIfPresent(partitionName);
            if (exists != null && exists) {
                log.debug("Partition exists in cache: {}", partitionName);
                return true;
            }
            
            // 检查分区是否存在
            if (checkPartitionExists(partitionName)) {
                partitionCache.put(partitionName, true);
                log.debug("Partition exists in database: {}", partitionName);
                return true;
            }
            
            // 创建分区
            boolean created = createPartition(dataType, pointType, dataTime, isMainTable, partitionName);
            if (created) {
                partitionCache.put(partitionName, true);
                log.info("Successfully created partition: {}", partitionName);
            }
            
            return created;
            
        } catch (Exception e) {
            log.error("Failed to ensure partition exists for dataType: {}, pointType: {}, dataTime: {}", 
                dataType, pointType, dataTime, e);
            return false;
        }
    }
    
    /**
     * 计算分区名称
     * @param dataType 数据类型
     * @param pointType 站点类型
     * @param dataTime 数据时间
     * @param isMainTable 是否为正式表
     * @return 分区名称
     */
    private String calculatePartitionName(String dataType, String pointType, 
                                        Timestamp dataTime, boolean isMainTable) {
        if (isMainTable) {
            // 正式表分区命名：env_{data_type}_gf_{point_type}_{time_period}
            String timePart = DateTimeUtils.calculatePartitionTimePart(dataTime, dataType);
            return String.format("env_%s_gf_%s_%s", dataType, pointType, timePart);
        } else {
            // 中间表分区命名：env_{data_type}_gf_nomapping_{YYYYMM}
            String timePart = DateTimeUtils.calculateMidTablePartitionTimePart(dataTime);
            return String.format("env_%s_gf_nomapping_%s", dataType, timePart);
        }
    }
    
    /**
     * 检查分区是否存在
     * @param partitionName 分区名称
     * @return true如果分区存在
     */
    private boolean checkPartitionExists(String partitionName) {
        String sql = """
            SELECT COUNT(1) FROM information_schema.tables 
            WHERE table_schema = 'env' 
            AND table_name = ?
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, partitionName);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            log.error("Failed to check partition existence: {}", partitionName, e);
        }
        return false;
    }
    
    /**
     * 创建分区
     * @param dataType 数据类型
     * @param pointType 站点类型
     * @param dataTime 数据时间
     * @param isMainTable 是否为正式表
     * @param partitionName 分区名称
     * @return true如果创建成功
     */
    private boolean createPartition(String dataType, String pointType, 
                                  Timestamp dataTime, boolean isMainTable, String partitionName) {
        try {
            String[] sqls = buildPartitionSqls(dataType, pointType, dataTime, 
                                             isMainTable, partitionName);
            
            connection.setAutoCommit(false);
            
            for (String sql : sqls) {
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.executeUpdate();
                    log.debug("Executed partition SQL: {}", sql);
                }
            }
            
            connection.commit();
            log.info("Successfully created partition: {}", partitionName);
            return true;
            
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                log.error("Failed to rollback partition creation", rollbackEx);
            }
            log.error("Failed to create partition: {}", partitionName, e);
            return false;
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                log.error("Failed to reset auto-commit", e);
            }
        }
    }
    
    /**
     * 构建分区创建SQL
     * @param dataType 数据类型
     * @param pointType 站点类型
     * @param dataTime 数据时间
     * @param isMainTable 是否为正式表
     * @param partitionName 分区名称
     * @return SQL语句数组
     */
    private String[] buildPartitionSqls(String dataType, String pointType, 
                                      Timestamp dataTime, boolean isMainTable, 
                                      String partitionName) {
        if (isMainTable) {
            return buildMainTablePartitionSqls(dataType, pointType, dataTime, partitionName);
        } else {
            return buildMidTablePartitionSqls(dataType, dataTime, partitionName);
        }
    }
    
    /**
     * 构建正式表分区SQL
     */
    private String[] buildMainTablePartitionSqls(String dataType, String pointType, 
                                               Timestamp dataTime, String partitionName) {
        // 计算分区时间范围
        String startTime = DateTimeUtils.calculatePartitionStartTime(dataTime, dataType);
        String endTime = DateTimeUtils.calculatePartitionEndTime(dataTime, dataType);
        
        String parentTable = String.format("env_%s_gf_%s", dataType, pointType);
        String pkColumns = "point_id, data_time, operation_customer_id, data_type, source";
        String indexName = String.format("idx_%s_dt", partitionName);
        
        return new String[] {
            // 创建分区表
            String.format("""
                CREATE TABLE env.%s PARTITION OF env.%s (
                    CONSTRAINT %s_pkey PRIMARY KEY (%s)
                ) FOR VALUES FROM ('%s') TO ('%s')
                """, partitionName, parentTable, partitionName, pkColumns, startTime, endTime),
            
            // 创建时间索引
            String.format("CREATE INDEX %s ON env.%s USING btree (data_time)", 
                indexName, partitionName)
        };
    }
    
    /**
     * 构建中间表分区SQL
     */
    private String[] buildMidTablePartitionSqls(String dataType, Timestamp dataTime, 
                                              String partitionName) {
        // 中间表统一按月分区
        String startTime = DateTimeUtils.calculateMidTablePartitionStartTime(dataTime);
        String endTime = DateTimeUtils.calculateMidTablePartitionEndTime(dataTime);
        
        String parentTable = String.format("env_%s_gf_nomapping", dataType);
        String pkColumns = "data_time, mncode";
        
        return new String[] {
            // 创建分区表
            String.format("""
                CREATE TABLE env.%s PARTITION OF env.%s (
                    CONSTRAINT %s_pkey PRIMARY KEY (%s)
                ) FOR VALUES FROM ('%s') TO ('%s')
                """, partitionName, parentTable, partitionName, pkColumns, startTime, endTime)
        };
    }
    
    /**
     * 获取缓存统计信息
     * @return 缓存大小
     */
    public long getCacheSize() {
        return partitionCache.size();
    }
    
    /**
     * 清理缓存
     */
    public void cleanupCache() {
        partitionCache.cleanUp();
        log.info("Cleaned up partition cache");
    }
}

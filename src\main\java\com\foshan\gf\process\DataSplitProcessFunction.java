package com.foshan.gf.process;

import com.foshan.gf.model.GFDataRecord;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据分流处理函数
 * 第一级分流：按data_type（cn字段）将数据分离到不同的输出流
 * - 2051: 分钟数据
 * - 2061: 小时数据  
 * - 2031: 日数据
 */
public class DataSplitProcessFunction extends ProcessFunction<GFDataRecord, GFDataRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(DataSplitProcessFunction.class);
    private static final long serialVersionUID = 1L;
    
    // 定义侧输出标签
    public static final OutputTag<GFDataRecord> MINUTE_DATA_TAG = 
        new OutputTag<GFDataRecord>("minute-data-2051") {};
    
    public static final OutputTag<GFDataRecord> HOUR_DATA_TAG = 
        new OutputTag<GFDataRecord>("hour-data-2061") {};
    
    public static final OutputTag<GFDataRecord> DAY_DATA_TAG = 
        new OutputTag<GFDataRecord>("day-data-2031") {};
    
    // 统计计数器
    private transient long minuteDataCount = 0;
    private transient long hourDataCount = 0;
    private transient long dayDataCount = 0;
    private transient long unknownDataCount = 0;
    private transient long totalProcessedCount = 0;
    
    @Override
    public void processElement(GFDataRecord record, Context ctx, Collector<GFDataRecord> out) throws Exception {
        totalProcessedCount++;
        
        // 获取数据类型
        String dataType = record.getCn();
        
        if (dataType == null || dataType.trim().isEmpty()) {
            log.warn("Data type (cn) is null or empty in record: {}", record.getId());
            unknownDataCount++;
            return;
        }
        
        // 根据数据类型进行分流
        switch (dataType.trim()) {
            case "2051":
                // 分钟数据
                ctx.output(MINUTE_DATA_TAG, record);
                minuteDataCount++;
                log.debug("Routed minute data record: {}", record.getId());
                break;
                
            case "2061":
                // 小时数据
                ctx.output(HOUR_DATA_TAG, record);
                hourDataCount++;
                log.debug("Routed hour data record: {}", record.getId());
                break;
                
            case "2031":
                // 日数据
                ctx.output(DAY_DATA_TAG, record);
                dayDataCount++;
                log.debug("Routed day data record: {}", record.getId());
                break;
                
            default:
                log.warn("Unknown data type: {} in record: {}", dataType, record.getId());
                unknownDataCount++;
                break;
        }
        
        // 定期打印统计信息
        if (totalProcessedCount % 10000 == 0) {
            log.info("Data split statistics - Total: {}, Minute: {}, Hour: {}, Day: {}, Unknown: {}", 
                totalProcessedCount, minuteDataCount, hourDataCount, dayDataCount, unknownDataCount);
        }
    }
    
    /**
     * 获取分钟数据输出标签
     */
    public static OutputTag<GFDataRecord> getMinuteDataTag() {
        return MINUTE_DATA_TAG;
    }
    
    /**
     * 获取小时数据输出标签
     */
    public static OutputTag<GFDataRecord> getHourDataTag() {
        return HOUR_DATA_TAG;
    }
    
    /**
     * 获取日数据输出标签
     */
    public static OutputTag<GFDataRecord> getDayDataTag() {
        return DAY_DATA_TAG;
    }
}

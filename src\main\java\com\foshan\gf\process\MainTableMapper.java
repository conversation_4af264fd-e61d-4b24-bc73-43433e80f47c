package com.foshan.gf.process;

import com.fasterxml.jackson.databind.JsonNode;
import com.foshan.gf.model.GFDataRecord;
import com.foshan.gf.model.MainTableRecord;
import com.foshan.gf.util.JsonUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

/**
 * 正式表字段映射器
 * 将GFDataRecord转换为MainTableRecord（point_id非空的数据）
 */
public class MainTableMapper implements MapFunction<GFDataRecord, MainTableRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(MainTableMapper.class);
    private static final long serialVersionUID = 1L;
    
    @Override
    public MainTableRecord map(GFDataRecord record) throws Exception {
        try {
            MainTableRecord mainRecord = new MainTableRecord();
            
            // 主键字段映射
            mainRecord.setPointId(parsePointId(record.getPointId()));
            mainRecord.setDataTime(record.getParsedDataTime());
            mainRecord.setOperationCustomerId(record.getOperationCustomerId());
            mainRecord.setDataType(record.getCn());
            mainRecord.setSource("nt"); // 固定值
            
            // 基础字段映射
            mainRecord.setQn(record.getId());                    // id -> qn
            mainRecord.setMncode(record.getMn());                // mn -> mncode
            mainRecord.setPointType(record.getPointType());      // point_type -> point_type
            mainRecord.setWriteTime(new Timestamp(System.currentTimeMillis())); // 当前时间
            
            // JSON字段映射
            mainRecord.setGeneJson(record.getFactors());         // factors -> gene_json
            
            // 构建gene_trans_json（反转gene_code_mapping）
            JsonNode geneTransJson = JsonUtils.reverseGeneCodeMapping(record.getGeneCodeMapping());
            mainRecord.setGeneTransJson(geneTransJson);
            
            // 构建main_ext（排除核心字段后的JSON）
            JsonNode originalJson = JsonUtils.parseJson(record.getRawJson());
            JsonNode mainExt = JsonUtils.buildMainExt(originalJson);
            mainRecord.setMainExt(mainExt);
            
            // 其他字段设置为null（使用数据库默认值）
            mainRecord.setSt(null);
            mainRecord.setFlag(null);
            mainRecord.setCpExt(null);
            mainRecord.setAutoAudit(null);
            mainRecord.setExtProperty(null);
            mainRecord.setGeneJsonStd(null);
            mainRecord.setWaterGeneLabel(null);
            mainRecord.setWaterLabel(null);
            mainRecord.setCommRule(null);
            mainRecord.setLabelJson(null);
            
            log.debug("Mapped main table record: {}", mainRecord);
            return mainRecord;
            
        } catch (Exception e) {
            log.error("Failed to map main table record: {}", record, e);
            throw e;
        }
    }
    
    /**
     * 解析point_id字符串为Integer
     * @param pointIdStr point_id字符串
     * @return Integer值，解析失败返回null
     */
    private Integer parsePointId(String pointIdStr) {
        if (pointIdStr == null || pointIdStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Integer.parseInt(pointIdStr.trim());
        } catch (NumberFormatException e) {
            log.error("Failed to parse point_id: {}", pointIdStr, e);
            return null;
        }
    }
}

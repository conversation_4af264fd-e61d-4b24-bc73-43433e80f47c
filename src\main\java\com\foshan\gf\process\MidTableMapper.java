package com.foshan.gf.process;

import com.fasterxml.jackson.databind.JsonNode;
import com.foshan.gf.model.GFDataRecord;
import com.foshan.gf.model.MidTableRecord;
import com.foshan.gf.util.JsonUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

/**
 * 中间表字段映射器
 * 将GFDataRecord转换为MidTableRecord（point_id为空的数据）
 */
public class MidTableMapper implements MapFunction<GFDataRecord, MidTableRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(MidTableMapper.class);
    private static final long serialVersionUID = 1L;
    
    @Override
    public MidTableRecord map(GFDataRecord record) throws Exception {
        try {
            MidTableRecord midRecord = new MidTableRecord();
            
            // 主键字段映射
            midRecord.setCustomerId(record.getCustomerId());     // customer_id -> customer_id
            midRecord.setMncode(record.getMn());                 // mn -> mncode
            midRecord.setDataTime(record.getParsedDataTime());   // data_time -> data_time
            
            // 基础字段映射
            midRecord.setDataType(record.getCn());               // cn -> data_type
            midRecord.setQn(record.getId());                     // id -> qn
            midRecord.setWriteTime(new Timestamp(System.currentTimeMillis())); // 当前时间
            
            // JSON字段映射
            midRecord.setGeneJson(record.getFactors());          // factors -> gene_json
            
            // 构建main_ext（排除核心字段后的JSON）
            JsonNode originalJson = JsonUtils.parseJson(record.getRawJson());
            JsonNode mainExt = JsonUtils.buildMainExt(originalJson);
            midRecord.setMainExt(mainExt);
            
            log.debug("Mapped mid table record: {}", midRecord);
            return midRecord;
            
        } catch (Exception e) {
            log.error("Failed to map mid table record: {}", record, e);
            throw e;
        }
    }
}

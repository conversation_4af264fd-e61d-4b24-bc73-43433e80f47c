package com.foshan.gf.process;

import com.foshan.gf.model.GFDataRecord;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 站点ID分流处理函数
 * 第二级分流：在每个data_type流中按point_id进行分离
 * - point_id非空: 路由到正式表处理链（主流输出）
 * - point_id为空: 路由到中间表处理链（侧输出）
 */
public class PointIdSplitFunction extends ProcessFunction<GFDataRecord, GFDataRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(PointIdSplitFunction.class);
    private static final long serialVersionUID = 1L;
    
    // 中间表数据侧输出标签
    public static final OutputTag<GFDataRecord> MID_TABLE_TAG = 
        new OutputTag<GFDataRecord>("mid-table-data") {};
    
    // 统计计数器
    private transient long mainTableCount = 0;
    private transient long midTableCount = 0;
    private transient long totalProcessedCount = 0;
    
    private final String dataType;
    
    public PointIdSplitFunction(String dataType) {
        this.dataType = dataType;
    }
    
    @Override
    public void processElement(GFDataRecord record, Context ctx, Collector<GFDataRecord> out) throws Exception {
        totalProcessedCount++;
        
        // 判断point_id是否为空
        if (record.hasPointId()) {
            // point_id非空，路由到正式表（主流）
            out.collect(record);
            mainTableCount++;
            log.debug("Routed to main table - DataType: {}, Record: {}", dataType, record.getId());
        } else {
            // point_id为空，路由到中间表（侧输出）
            ctx.output(MID_TABLE_TAG, record);
            midTableCount++;
            log.debug("Routed to mid table - DataType: {}, Record: {}", dataType, record.getId());
        }
        
        // 定期打印统计信息
        if (totalProcessedCount % 5000 == 0) {
            log.info("Point ID split statistics for {} - Total: {}, Main table: {}, Mid table: {}", 
                dataType, totalProcessedCount, mainTableCount, midTableCount);
        }
    }
    
    /**
     * 获取中间表数据输出标签
     */
    public static OutputTag<GFDataRecord> getMidTableTag() {
        return MID_TABLE_TAG;
    }
    
    /**
     * 获取数据类型
     */
    public String getDataType() {
        return dataType;
    }
}

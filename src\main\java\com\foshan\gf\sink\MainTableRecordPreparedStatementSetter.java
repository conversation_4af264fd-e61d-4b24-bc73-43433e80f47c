package com.foshan.gf.sink;

import com.foshan.gf.model.MainTableRecord;
import com.foshan.gf.util.JsonUtils;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;

/**
 * 正式表记录PreparedStatement设置器
 * 用于设置MainTableRecord的SQL参数
 */
public class MainTableRecordPreparedStatementSetter implements JdbcStatementBuilder<MainTableRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(MainTableRecordPreparedStatementSetter.class);
    private static final long serialVersionUID = 1L;
    
    private final String dataType;
    
    public MainTableRecordPreparedStatementSetter(String dataType) {
        this.dataType = dataType;
    }
    
    @Override
    public void accept(PreparedStatement ps, MainTableRecord record) throws SQLException {
        try {
            // INSERT部分的参数设置
            // INSERT INTO env.env_{data_type}_gf (
            //     point_id, data_time, data_type, qn, mncode, gene_json,
            //     main_ext, point_type, write_time, source, operation_customer_id,
            //     gene_trans_json
            // ) VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?, ?, ?, ?, ?::jsonb)
            
            int paramIndex = 1;
            
            // 1. point_id
            if (record.getPointId() != null) {
                ps.setInt(paramIndex++, record.getPointId());
            } else {
                ps.setNull(paramIndex++, Types.INTEGER);
            }
            
            // 2. data_time
            if (record.getDataTime() != null) {
                ps.setTimestamp(paramIndex++, record.getDataTime());
            } else {
                ps.setNull(paramIndex++, Types.TIMESTAMP);
            }
            
            // 3. data_type
            ps.setString(paramIndex++, record.getDataType());
            
            // 4. qn
            ps.setString(paramIndex++, record.getQn());
            
            // 5. mncode
            ps.setString(paramIndex++, record.getMncode());
            
            // 6. gene_json (JSONB)
            if (record.getGeneJson() != null) {
                ps.setString(paramIndex++, JsonUtils.toJsonString(record.getGeneJson()));
            } else {
                ps.setNull(paramIndex++, Types.OTHER);
            }
            
            // 7. main_ext (JSONB)
            if (record.getMainExt() != null) {
                ps.setString(paramIndex++, JsonUtils.toJsonString(record.getMainExt()));
            } else {
                ps.setNull(paramIndex++, Types.OTHER);
            }
            
            // 8. point_type
            ps.setString(paramIndex++, record.getPointType());
            
            // 9. write_time
            if (record.getWriteTime() != null) {
                ps.setTimestamp(paramIndex++, record.getWriteTime());
            } else {
                ps.setNull(paramIndex++, Types.TIMESTAMP);
            }
            
            // 10. source
            ps.setString(paramIndex++, record.getSource());
            
            // 11. operation_customer_id
            ps.setString(paramIndex++, record.getOperationCustomerId());
            
            // 12. gene_trans_json (JSONB)
            if (record.getGeneTransJson() != null) {
                ps.setString(paramIndex++, JsonUtils.toJsonString(record.getGeneTransJson()));
            } else {
                ps.setString(paramIndex++, "{}"); // 默认空JSON对象
            }
            
            log.debug("Set parameters for main table record: pointId={}, dataTime={}, dataType={}", 
                record.getPointId(), record.getDataTime(), record.getDataType());
                
        } catch (Exception e) {
            log.error("Failed to set parameters for main table record: {}", record, e);
            throw new SQLException("Failed to set parameters", e);
        }
    }
}

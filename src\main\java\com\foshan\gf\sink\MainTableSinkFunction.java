package com.foshan.gf.sink;

import com.foshan.gf.config.PostgreSQLConfig;
import com.foshan.gf.model.MainTableRecord;
import com.foshan.gf.partition.PartitionManager;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 正式表Sink函数（带分区管理）
 * 负责将MainTableRecord写入PostgreSQL正式表，并自动管理分区
 */
public class MainTableSinkFunction extends RichSinkFunction<MainTableRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(MainTableSinkFunction.class);
    private static final long serialVersionUID = 1L;
    
    private final String dataType;
    private final PostgreSQLConfig config;
    
    private transient Connection connection;
    private transient PartitionManager partitionManager;
    private transient PreparedStatement preparedStatement;
    private transient List<MainTableRecord> batch;
    private transient long lastBatchTime;
    
    // 统计计数器
    private transient long recordsWritten = 0;
    private transient long batchesWritten = 0;
    private transient long partitionsCreated = 0;
    
    public MainTableSinkFunction(String dataType) {
        this.dataType = dataType;
        this.config = new PostgreSQLConfig();
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化数据库连接
        initConnection();
        
        // 初始化分区管理器
        partitionManager = new PartitionManager(connection);
        
        // 初始化批处理
        batch = new ArrayList<>(config.getBatchSize());
        lastBatchTime = System.currentTimeMillis();
        
        // 准备SQL语句
        String insertSql = buildInsertSql();
        preparedStatement = connection.prepareStatement(insertSql);
        
        log.info("Opened MainTableSinkFunction for data type: {}", dataType);
    }
    
    @Override
    public void invoke(MainTableRecord record, Context context) throws Exception {
        try {
            // 确保分区存在
            boolean partitionReady = ensurePartitionExists(record);
            if (!partitionReady) {
                log.error("Failed to ensure partition exists for record: {}", record);
                return;
            }
            
            // 添加到批处理
            batch.add(record);
            
            // 检查是否需要执行批处理
            if (shouldFlushBatch()) {
                flushBatch();
            }
            
        } catch (Exception e) {
            log.error("Failed to process main table record: {}", record, e);
            throw e;
        }
    }
    
    @Override
    public void close() throws Exception {
        try {
            // 刷新剩余的批处理数据
            if (batch != null && !batch.isEmpty()) {
                flushBatch();
            }
            
            // 关闭资源
            if (preparedStatement != null) {
                preparedStatement.close();
            }
            if (connection != null) {
                connection.close();
            }
            
            log.info("Closed MainTableSinkFunction for data type: {}. " +
                    "Records written: {}, Batches: {}, Partitions created: {}", 
                dataType, recordsWritten, batchesWritten, partitionsCreated);
                
        } catch (Exception e) {
            log.error("Error closing MainTableSinkFunction", e);
        } finally {
            super.close();
        }
    }
    
    /**
     * 初始化数据库连接
     */
    private void initConnection() throws SQLException {
        connection = DriverManager.getConnection(
            config.getUrl(), 
            config.getUsername(), 
            config.getPassword()
        );
        connection.setAutoCommit(true);
        log.info("Initialized database connection for main table sink");
    }
    
    /**
     * 确保分区存在
     */
    private boolean ensurePartitionExists(MainTableRecord record) {
        try {
            boolean exists = partitionManager.ensurePartitionExists(
                record.getDataType(),
                record.getPointType(),
                record.getDataTime(),
                true // 正式表
            );
            
            if (exists) {
                return true;
            } else {
                partitionsCreated++;
                return partitionManager.ensurePartitionExists(
                    record.getDataType(),
                    record.getPointType(),
                    record.getDataTime(),
                    true
                );
            }
        } catch (Exception e) {
            log.error("Failed to ensure partition exists", e);
            return false;
        }
    }
    
    /**
     * 检查是否应该刷新批处理
     */
    private boolean shouldFlushBatch() {
        return batch.size() >= config.getBatchSize() || 
               (System.currentTimeMillis() - lastBatchTime) >= config.getBatchIntervalMs();
    }
    
    /**
     * 刷新批处理数据
     */
    private void flushBatch() throws SQLException {
        if (batch.isEmpty()) {
            return;
        }
        
        try {
            connection.setAutoCommit(false);
            
            for (MainTableRecord record : batch) {
                setParameters(preparedStatement, record);
                preparedStatement.addBatch();
            }
            
            int[] results = preparedStatement.executeBatch();
            connection.commit();
            
            recordsWritten += batch.size();
            batchesWritten++;
            
            log.debug("Flushed batch of {} main table records for data type: {}", 
                batch.size(), dataType);
            
            batch.clear();
            lastBatchTime = System.currentTimeMillis();
            
        } catch (SQLException e) {
            connection.rollback();
            log.error("Failed to flush batch for data type: {}", dataType, e);
            throw e;
        } finally {
            connection.setAutoCommit(true);
        }
    }
    
    /**
     * 设置PreparedStatement参数
     */
    private void setParameters(PreparedStatement ps, MainTableRecord record) throws SQLException {
        MainTableRecordPreparedStatementSetter setter = new MainTableRecordPreparedStatementSetter(dataType);
        setter.accept(ps, record);
    }
    
    /**
     * 构建插入SQL
     */
    private String buildInsertSql() {
        PostgreSQLSinkBuilder builder = new PostgreSQLSinkBuilder();
        // 这里我们需要从builder中提取SQL，但由于架构限制，我们直接构建
        return String.format("""
            INSERT INTO env.env_%s_gf (
                point_id, data_time, data_type, qn, mncode, gene_json,
                main_ext, point_type, write_time, source, operation_customer_id,
                gene_trans_json
            ) VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?, ?, ?, ?, ?::jsonb)
            ON CONFLICT (point_id, data_time, operation_customer_id, data_type, source)
            DO UPDATE SET
                qn = EXCLUDED.qn,
                mncode = EXCLUDED.mncode,
                gene_json = EXCLUDED.gene_json,
                main_ext = EXCLUDED.main_ext,
                point_type = EXCLUDED.point_type,
                write_time = EXCLUDED.write_time,
                gene_trans_json = EXCLUDED.gene_trans_json
            """, dataType);
    }
}

package com.foshan.gf.sink;

import com.foshan.gf.model.MidTableRecord;
import com.foshan.gf.util.JsonUtils;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;

/**
 * 中间表记录PreparedStatement设置器
 * 用于设置MidTableRecord的SQL参数
 */
public class MidTableRecordPreparedStatementSetter implements JdbcStatementBuilder<MidTableRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(MidTableRecordPreparedStatementSetter.class);
    private static final long serialVersionUID = 1L;
    
    private final String dataType;
    
    public MidTableRecordPreparedStatementSetter(String dataType) {
        this.dataType = dataType;
    }
    
    @Override
    public void accept(PreparedStatement ps, MidTableRecord record) throws SQLException {
        try {
            // INSERT部分的参数设置
            // INSERT INTO env.env_{data_type}_gf_nomapping (
            //     customer_id, mncode, data_time, data_type, qn, gene_json, main_ext, write_time
            // ) VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?)
            
            int paramIndex = 1;
            
            // 1. customer_id
            ps.setString(paramIndex++, record.getCustomerId());
            
            // 2. mncode
            ps.setString(paramIndex++, record.getMncode());
            
            // 3. data_time
            if (record.getDataTime() != null) {
                ps.setTimestamp(paramIndex++, record.getDataTime());
            } else {
                ps.setNull(paramIndex++, Types.TIMESTAMP);
            }
            
            // 4. data_type
            ps.setString(paramIndex++, record.getDataType());
            
            // 5. qn
            ps.setString(paramIndex++, record.getQn());
            
            // 6. gene_json (JSONB)
            if (record.getGeneJson() != null) {
                ps.setString(paramIndex++, JsonUtils.toJsonString(record.getGeneJson()));
            } else {
                ps.setNull(paramIndex++, Types.OTHER);
            }
            
            // 7. main_ext (JSONB)
            if (record.getMainExt() != null) {
                ps.setString(paramIndex++, JsonUtils.toJsonString(record.getMainExt()));
            } else {
                ps.setNull(paramIndex++, Types.OTHER);
            }
            
            // 8. write_time
            if (record.getWriteTime() != null) {
                ps.setTimestamp(paramIndex++, record.getWriteTime());
            } else {
                ps.setNull(paramIndex++, Types.TIMESTAMP);
            }
            
            log.debug("Set parameters for mid table record: customerId={}, mncode={}, dataTime={}, dataType={}", 
                record.getCustomerId(), record.getMncode(), record.getDataTime(), record.getDataType());
                
        } catch (Exception e) {
            log.error("Failed to set parameters for mid table record: {}", record, e);
            throw new SQLException("Failed to set parameters", e);
        }
    }
}

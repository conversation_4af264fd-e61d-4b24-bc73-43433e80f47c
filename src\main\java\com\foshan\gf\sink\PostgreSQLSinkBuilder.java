package com.foshan.gf.sink;

import com.foshan.gf.config.PostgreSQLConfig;
import com.foshan.gf.model.MainTableRecord;
import com.foshan.gf.model.MidTableRecord;
import org.apache.flink.connector.jdbc.JdbcSink;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * PostgreSQL Sink构建器
 * 负责创建正式表和中间表的Sink函数
 */
public class PostgreSQLSinkBuilder {
    
    private static final Logger log = LoggerFactory.getLogger(PostgreSQLSinkBuilder.class);
    
    private final PostgreSQLConfig config;
    
    public PostgreSQLSinkBuilder() {
        this.config = new PostgreSQLConfig();
    }
    
    /**
     * 构建正式表Sink
     * @param dataType 数据类型（2051/2061/2031）
     * @return 正式表SinkFunction
     */
    public SinkFunction<MainTableRecord> buildMainTableSink(String dataType) {
        String insertSql = buildMainTableInsertSql(dataType);
        
        log.info("Building main table sink for data type: {}", dataType);
        log.debug("Main table insert SQL: {}", insertSql);
        
        return JdbcSink.sink(
            insertSql,
            new MainTableRecordPreparedStatementSetter(dataType),
            config.getExecutionOptions(),
            config.getConnectionOptions()
        );
    }
    
    /**
     * 构建中间表Sink
     * @param dataType 数据类型（2051/2061/2031）
     * @return 中间表SinkFunction
     */
    public SinkFunction<MidTableRecord> buildMidTableSink(String dataType) {
        String insertSql = buildMidTableInsertSql(dataType);
        
        log.info("Building mid table sink for data type: {}", dataType);
        log.debug("Mid table insert SQL: {}", insertSql);
        
        return JdbcSink.sink(
            insertSql,
            new MidTableRecordPreparedStatementSetter(dataType),
            config.getExecutionOptions(),
            config.getConnectionOptions()
        );
    }
    
    /**
     * 构建正式表插入SQL（带UPSERT）
     * @param dataType 数据类型
     * @return INSERT SQL语句
     */
    private String buildMainTableInsertSql(String dataType) {
        return String.format("""
            INSERT INTO env.env_%s_gf (
                point_id, data_time, data_type, qn, mncode, gene_json,
                main_ext, point_type, write_time, source, operation_customer_id,
                gene_trans_json
            ) VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?, ?, ?, ?, ?::jsonb)
            ON CONFLICT (point_id, data_time, operation_customer_id, data_type, source)
            DO UPDATE SET
                qn = EXCLUDED.qn,
                mncode = EXCLUDED.mncode,
                gene_json = EXCLUDED.gene_json,
                main_ext = EXCLUDED.main_ext,
                point_type = EXCLUDED.point_type,
                write_time = EXCLUDED.write_time,
                gene_trans_json = EXCLUDED.gene_trans_json
            """, dataType);
    }
    
    /**
     * 构建中间表插入SQL（带UPSERT）
     * @param dataType 数据类型
     * @return INSERT SQL语句
     */
    private String buildMidTableInsertSql(String dataType) {
        return String.format("""
            INSERT INTO env.env_%s_gf_nomapping (
                customer_id, mncode, data_time, data_type, qn, gene_json, main_ext, write_time
            ) VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?)
            ON CONFLICT (data_time, mncode)
            DO UPDATE SET
                customer_id = EXCLUDED.customer_id,
                data_type = EXCLUDED.data_type,
                qn = EXCLUDED.qn,
                gene_json = EXCLUDED.gene_json,
                main_ext = EXCLUDED.main_ext,
                write_time = EXCLUDED.write_time
            """, dataType);
    }
    
    /**
     * 获取PostgreSQL配置
     * @return PostgreSQLConfig对象
     */
    public PostgreSQLConfig getConfig() {
        return config;
    }
}

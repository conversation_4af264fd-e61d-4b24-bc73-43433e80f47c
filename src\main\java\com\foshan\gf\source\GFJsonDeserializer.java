package com.foshan.gf.source;

import com.fasterxml.jackson.databind.JsonNode;
import com.foshan.gf.model.GFDataRecord;
import com.foshan.gf.util.DateTimeUtils;
import com.foshan.gf.util.JsonUtils;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Timestamp;

/**
 * 国发数据JSON反序列化器
 * 将Kafka中的JSON字符串转换为GFDataRecord对象
 */
public class GFJsonDeserializer implements DeserializationSchema<GFDataRecord> {
    
    private static final Logger log = LoggerFactory.getLogger(GFJsonDeserializer.class);
    private static final long serialVersionUID = 1L;
    
    @Override
    public GFDataRecord deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            log.warn("Received empty message, skipping");
            return null;
        }
        
        String jsonStr = new String(message, "UTF-8");
        return parseGFDataRecord(jsonStr);
    }
    
    /**
     * 解析JSON字符串为GFDataRecord对象
     * @param jsonStr JSON字符串
     * @return GFDataRecord对象，解析失败返回null
     */
    private GFDataRecord parseGFDataRecord(String jsonStr) {
        try {
            JsonNode jsonNode = JsonUtils.parseJson(jsonStr);
            if (jsonNode == null) {
                log.error("Failed to parse JSON: {}", jsonStr);
                return null;
            }
            
            GFDataRecord record = new GFDataRecord();
            
            // 设置原始JSON
            record.setRawJson(jsonStr);
            
            // 解析基础字段
            record.setId(JsonUtils.getTextValue(jsonNode, "id"));
            record.setPointId(JsonUtils.getTextValue(jsonNode, "point_id"));
            record.setPointType(JsonUtils.getTextValue(jsonNode, "point_type"));
            record.setOperationCustomerId(JsonUtils.getTextValue(jsonNode, "operation_customer_id"));
            record.setCustomerId(JsonUtils.getTextValue(jsonNode, "customer_id"));
            record.setMn(JsonUtils.getTextValue(jsonNode, "mn"));
            record.setCn(JsonUtils.getTextValue(jsonNode, "cn"));
            record.setDataTime(JsonUtils.getTextValue(jsonNode, "data_time"));
            record.setGeneCodeMapping(JsonUtils.getTextValue(jsonNode, "gene_code_mapping"));
            
            // 解析factors字段
            JsonNode factorsNode = JsonUtils.getJsonNode(jsonNode, "factors");
            record.setFactors(factorsNode);
            
            // 解析其他字段
            record.setPwd(JsonUtils.getTextValue(jsonNode, "pwd"));
            record.setOutletCode(JsonUtils.getTextValue(jsonNode, "outlet_code"));
            record.setWorkcordSc(JsonUtils.getTextValue(jsonNode, "workcordSc"));
            record.setZdWorkcordSc(JsonUtils.getTextValue(jsonNode, "zd_workcordSc"));
            record.setZdWorkcordZl(JsonUtils.getTextValue(jsonNode, "zd_workcordZl"));
            record.setWorkcordZl(JsonUtils.getTextValue(jsonNode, "workcordZl"));
            record.setRgWorkcordSc(JsonUtils.getTextValue(jsonNode, "rg_workcordSc"));
            record.setRgWorkcordZl(JsonUtils.getTextValue(jsonNode, "rg_workcordZl"));
            record.setLogTime(JsonUtils.getTextValue(jsonNode, "log_time"));
            
            // 解析和转换时间
            String dataTimeStr = record.getDataTime();
            if (dataTimeStr != null && !dataTimeStr.trim().isEmpty()) {
                Timestamp parsedTime = DateTimeUtils.parseDataTime(dataTimeStr);
                record.setParsedDataTime(parsedTime);
            } else {
                log.warn("Data time is missing in record: {}", record.getId());
                record.setParsedDataTime(new Timestamp(System.currentTimeMillis()));
            }
            
            // 生成分区键（用于后续分区管理）
            generatePartitionKey(record);
            
            // 验证必要字段
            if (!validateRecord(record)) {
                log.error("Record validation failed: {}", record);
                return null;
            }
            
            log.debug("Successfully parsed record: {}", record);
            return record;
            
        } catch (Exception e) {
            log.error("Failed to parse GF data record from JSON: {}", jsonStr, e);
            return null;
        }
    }
    
    /**
     * 生成分区键
     * @param record GF数据记录
     */
    private void generatePartitionKey(GFDataRecord record) {
        StringBuilder keyBuilder = new StringBuilder();
        
        // 数据类型
        if (record.getCn() != null) {
            keyBuilder.append(record.getCn());
        }
        
        keyBuilder.append("_");
        
        // 站点类型（如果有point_id）
        if (record.hasPointId() && record.getPointType() != null) {
            keyBuilder.append(record.getPointType());
        } else {
            keyBuilder.append("nomapping");
        }
        
        keyBuilder.append("_");
        
        // 时间部分
        if (record.getParsedDataTime() != null) {
            if (record.hasPointId()) {
                // 正式表的时间分区
                String timePart = DateTimeUtils.calculatePartitionTimePart(
                    record.getParsedDataTime(), record.getCn());
                keyBuilder.append(timePart);
            } else {
                // 中间表的时间分区
                String timePart = DateTimeUtils.calculateMidTablePartitionTimePart(
                    record.getParsedDataTime());
                keyBuilder.append(timePart);
            }
        }
        
        record.setPartitionKey(keyBuilder.toString());
    }
    
    /**
     * 验证记录的必要字段
     * @param record GF数据记录
     * @return true如果验证通过
     */
    private boolean validateRecord(GFDataRecord record) {
        // 检查MN码
        if (JsonUtils.isEmpty(record.getMn())) {
            log.error("MN code is missing in record: {}", record.getId());
            return false;
        }
        
        // 检查数据时间
        if (record.getParsedDataTime() == null) {
            log.error("Data time is missing or invalid in record: {}", record.getId());
            return false;
        }
        
        // 检查数据类型
        String dataType = record.getCn();
        if (JsonUtils.isEmpty(dataType)) {
            log.error("Data type (cn) is missing in record: {}", record.getId());
            return false;
        }
        
        // 验证数据类型是否为支持的类型
        if (!"2051".equals(dataType) && !"2061".equals(dataType) && !"2031".equals(dataType)) {
            log.warn("Unknown data type: {} in record: {}", dataType, record.getId());
            // 不返回false，允许未知类型通过，但记录警告
        }
        
        // 如果没有point_id，必须有customer_id
        if (!record.hasPointId() && JsonUtils.isEmpty(record.getCustomerId())) {
            log.error("Both point_id and customer_id are missing in record: {}", record.getId());
            return false;
        }
        
        return true;
    }
    
    @Override
    public boolean isEndOfStream(GFDataRecord nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<GFDataRecord> getProducedType() {
        return TypeInformation.of(GFDataRecord.class);
    }
}

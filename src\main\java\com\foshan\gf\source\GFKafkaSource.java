package com.foshan.gf.source;

import com.foshan.gf.config.KafkaConfig;
import com.foshan.gf.model.GFDataRecord;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 国发数据Kafka源
 * 负责创建和配置Kafka数据源
 */
public class GFKafkaSource {
    
    private static final Logger log = LoggerFactory.getLogger(GFKafkaSource.class);
    
    private final KafkaConfig kafkaConfig;
    
    public GFKafkaSource() {
        this.kafkaConfig = new KafkaConfig();
    }
    
    /**
     * 创建Kafka数据源
     * @return KafkaSource对象
     */
    public KafkaSource<GFDataRecord> createKafkaSource() {
        log.info("Creating Kafka source with config: bootstrap-servers={}, topic={}, group-id={}", 
            kafkaConfig.getBootstrapServers(), kafkaConfig.getTopic(), kafkaConfig.getGroupId());
        
        return KafkaSource.<GFDataRecord>builder()
                .setBootstrapServers(kafkaConfig.getBootstrapServers())
                .setTopics(kafkaConfig.getTopic())
                .setGroupId(kafkaConfig.getGroupId())
                .setStartingOffsets(getOffsetsInitializer())
                .setValueOnlyDeserializer(new GFJsonDeserializer())
                .setProperties(kafkaConfig.getConsumerProperties())
                .build();
    }
    
    /**
     * 创建数据流
     * @param env Flink执行环境
     * @return 国发数据流
     */
    public DataStream<GFDataRecord> createDataStream(StreamExecutionEnvironment env) {
        KafkaSource<GFDataRecord> kafkaSource = createKafkaSource();
        
        // 创建数据流，使用无水位线模式（适应历史数据乱序场景）
        DataStream<GFDataRecord> dataStream = env
                .fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "GF-Kafka-Source")
                .name("GF-Kafka-Source")
                .uid("gf-kafka-source");
        
        log.info("Created GF data stream from Kafka source");
        return dataStream;
    }
    
    /**
     * 获取偏移量初始化器
     * @return OffsetsInitializer
     */
    private OffsetsInitializer getOffsetsInitializer() {
        String autoOffsetReset = kafkaConfig.getAutoOffsetReset();
        
        switch (autoOffsetReset.toLowerCase()) {
            case "earliest":
                log.info("Using earliest offset initializer");
                return OffsetsInitializer.earliest();
                
            case "latest":
                log.info("Using latest offset initializer");
                return OffsetsInitializer.latest();
                
            default:
                log.warn("Unknown auto.offset.reset value: {}, using latest", autoOffsetReset);
                return OffsetsInitializer.latest();
        }
    }
    
    /**
     * 获取Kafka配置
     * @return KafkaConfig对象
     */
    public KafkaConfig getKafkaConfig() {
        return kafkaConfig;
    }
}

package com.foshan.gf.test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.sql.Timestamp;

/**
 * Basic functionality test
 * Test core logic without external dependencies
 */
public class BasicTest {
    
    public static void main(String[] args) {
        System.out.println("=== Basic Function Test ===");

        // 1. Test time parsing
        testTimeParser();

        // 2. Test partition calculation
        testPartitionCalculation();

        // 3. Test string processing
        testStringProcessing();

        System.out.println("\n=== Basic Function Test Completed ===");
    }
    
    /**
     * Test time parsing functionality
     */
    private static void testTimeParser() {
        System.out.println("\n1. Test time parsing:");
        
        String[] testTimes = {
            "20250730140000",
            "20250731000000", 
            "20250801120000"
        };
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        
        for (String timeStr : testTimes) {
            try {
                LocalDateTime localTime = LocalDateTime.parse(timeStr, formatter);
                Timestamp timestamp = Timestamp.valueOf(localTime);
                System.out.println("  " + timeStr + " -> " + timestamp);
            } catch (Exception e) {
                System.out.println("  " + timeStr + " -> Parse failed: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试分区计算功能
     */
    private static void testPartitionCalculation() {
        System.out.println("\n2. 测试分区计算:");
        
        String timeStr = "20250730140000";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime localTime = LocalDateTime.parse(timeStr, formatter);
        
        // 测试不同数据类型的分区计算
        String[] dataTypes = {"2051", "2061", "2031"};
        
        for (String dataType : dataTypes) {
            System.out.println("  数据类型: " + dataType);
            
            switch (dataType) {
                case "2051": // 分钟数据按月分区
                    String monthStart = String.format("%04d-%02d-01 00:00:00", 
                        localTime.getYear(), localTime.getMonthValue());
                    String monthEnd = String.format("%04d-%02d-01 00:00:00", 
                        localTime.plusMonths(1).getYear(), localTime.plusMonths(1).getMonthValue());
                    String monthPart = String.format("%04d%02d", localTime.getYear(), localTime.getMonthValue());
                    System.out.println("    分区范围: " + monthStart + " 到 " + monthEnd);
                    System.out.println("    时间部分: " + monthPart);
                    break;
                    
                case "2061": // 小时数据按年分区
                    String yearStart = String.format("%04d-01-01 00:00:00", localTime.getYear());
                    String yearEnd = String.format("%04d-01-01 00:00:00", localTime.getYear() + 1);
                    String yearPart = String.valueOf(localTime.getYear());
                    System.out.println("    分区范围: " + yearStart + " 到 " + yearEnd);
                    System.out.println("    时间部分: " + yearPart);
                    break;
                    
                case "2031": // 日数据不按时间分区
                    System.out.println("    分区范围: 1970-01-01 00:00:00 到 2099-12-31 23:59:59");
                    System.out.println("    时间部分: default");
                    break;
            }
        }
    }
    
    /**
     * 测试字符串处理功能
     */
    private static void testStringProcessing() {
        System.out.println("\n3. 测试字符串处理:");
        
        // 测试point_id判断
        String[] pointIds = {"", "  ", null, "11674", "0"};
        
        for (String pointId : pointIds) {
            boolean hasPointId = pointId != null && !pointId.trim().isEmpty();
            System.out.println("  Point ID: '" + pointId + "' -> 有效: " + hasPointId);
        }
        
        // 测试分区名称生成
        System.out.println("\n  分区名称生成测试:");
        String dataType = "2061";
        String pointType = "31";
        String timePart = "2025";
        
        // 正式表分区名
        String mainPartition = String.format("env_%s_gf_%s_%s", dataType, pointType, timePart);
        System.out.println("    正式表分区: " + mainPartition);
        
        // 中间表分区名
        String midPartition = String.format("env_%s_gf_nomapping_%s", dataType, "202507");
        System.out.println("    中间表分区: " + midPartition);
    }
}

package com.foshan.gf.test;

import com.foshan.gf.util.DateTimeUtils;
import com.foshan.gf.util.JsonUtils;

import java.sql.Timestamp;

/**
 * Compilation test for Java 8 compatibility
 */
public class CompileTest {
    
    public static void main(String[] args) {
        System.out.println("=== Java 8 Compatibility Test ===");
        
        // Test DateTimeUtils
        testDateTimeUtils();
        
        // Test JsonUtils
        testJsonUtils();
        
        // Test partition naming
        testPartitionNaming();
        
        System.out.println("\n=== All Tests Passed ===");
    }
    
    private static void testDateTimeUtils() {
        System.out.println("\n1. Testing DateTimeUtils:");
        
        String timeStr = "20250730140000";
        Timestamp timestamp = DateTimeUtils.parseDataTime(timeStr);
        System.out.println("  Parsed time: " + timestamp);
        
        String startTime = DateTimeUtils.calculatePartitionStartTime(timestamp, "2061");
        String endTime = DateTimeUtils.calculatePartitionEndTime(timestamp, "2061");
        String timePart = DateTimeUtils.calculatePartitionTimePart(timestamp, "2061");
        
        System.out.println("  Partition start: " + startTime);
        System.out.println("  Partition end: " + endTime);
        System.out.println("  Time part: " + timePart);
    }
    
    private static void testJsonUtils() {
        System.out.println("\n2. Testing JsonUtils:");
        
        String testJson = "{\"key1\":\"value1\",\"key2\":\"value2\"}";
        System.out.println("  Test JSON: " + testJson);
        
        boolean isEmpty1 = JsonUtils.isEmpty("");
        boolean isEmpty2 = JsonUtils.isEmpty("test");
        boolean isEmpty3 = JsonUtils.isEmpty(null);
        
        System.out.println("  isEmpty(''): " + isEmpty1);
        System.out.println("  isEmpty('test'): " + isEmpty2);
        System.out.println("  isEmpty(null): " + isEmpty3);
    }
    
    private static void testPartitionNaming() {
        System.out.println("\n3. Testing Partition Naming:");
        
        String dataType = "2061";
        String pointType = "31";
        String timePart = "2025";
        
        // Main table partition
        String mainPartition = String.format("env_%s_gf_%s_%s", dataType, pointType, timePart);
        System.out.println("  Main table partition: " + mainPartition);
        
        // Mid table partition
        String midPartition = String.format("env_%s_gf_nomapping_%s", dataType, "202507");
        System.out.println("  Mid table partition: " + midPartition);
        
        // SQL building test
        String insertSql = String.format("INSERT INTO env.env_%s_gf (" +
                                       "point_id, data_time, data_type" +
                                       ") VALUES (?, ?, ?)", dataType);
        System.out.println("  Sample SQL: " + insertSql);
    }
}

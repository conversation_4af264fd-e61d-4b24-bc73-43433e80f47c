package com.foshan.gf.test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.gf.util.JsonUtils;
import com.foshan.gf.util.DateTimeUtils;

import java.sql.Timestamp;

/**
 * 简单的JSON解析测试
 * 不依赖Flink，直接测试核心逻辑
 */
public class SimpleJsonTest {
    
    public static void main(String[] args) {
        System.out.println("=== 开始JSON解析测试 ===");
        
        // 测试数据（来自设计方案）
        String testJson = """
            {"id":"38a81b6672ae439781c3860be1e4aad0","customer_id":"1674586959261044737","mn":"12315ctsy001","point_id":"11674","point_type":"31","operation_customer_id":"1674586959261044737","gene_code_mapping":"{\\"01\\": \\"NC147\\", \\"02\\": \\"NC50\\", \\"03\\": \\"NC123\\", \\"S01\\": \\"NC120\\", \\"S08\\": \\"NC149\\"}","pt_mn":"12315ctsy001","data_pwd":"12345611674","outlet_code":"","cn":"2061","data_time":"20250730140000","workcordSc":"","zd_workcordSc":"","zd_workcordZl":"","workcordZl":"","rg_workcordSc":"","rg_workcordZl":"","log_time":"","factors":{"01":{"factor_code":"01","rtd_value":"","avg_value":"113.29"},"02":{"factor_code":"02","rtd_value":"","avg_value":"178.05"}}}
            """;
        
        try {
            // 1. 测试JSON解析
            System.out.println("1. 测试JSON解析:");
            JsonNode jsonNode = JsonUtils.parseJson(testJson);
            if (jsonNode != null) {
                System.out.println("✓ JSON解析成功");
                
                // 提取关键字段
                String id = JsonUtils.getTextValue(jsonNode, "id");
                String pointId = JsonUtils.getTextValue(jsonNode, "point_id");
                String pointType = JsonUtils.getTextValue(jsonNode, "point_type");
                String mn = JsonUtils.getTextValue(jsonNode, "mn");
                String cn = JsonUtils.getTextValue(jsonNode, "cn");
                String dataTime = JsonUtils.getTextValue(jsonNode, "data_time");
                String geneCodeMapping = JsonUtils.getTextValue(jsonNode, "gene_code_mapping");
                
                System.out.println("  ID: " + id);
                System.out.println("  Point ID: " + pointId);
                System.out.println("  Point Type: " + pointType);
                System.out.println("  MN: " + mn);
                System.out.println("  CN (数据类型): " + cn);
                System.out.println("  Data Time: " + dataTime);
                System.out.println("  Gene Code Mapping: " + geneCodeMapping);
                
                // 2. 测试时间解析
                System.out.println("\n2. 测试时间解析:");
                Timestamp parsedTime = DateTimeUtils.parseDataTime(dataTime);
                System.out.println("  解析后时间: " + parsedTime);
                
                // 3. 测试分区时间计算
                System.out.println("\n3. 测试分区时间计算:");
                String startTime = DateTimeUtils.calculatePartitionStartTime(parsedTime, cn);
                String endTime = DateTimeUtils.calculatePartitionEndTime(parsedTime, cn);
                String timePart = DateTimeUtils.calculatePartitionTimePart(parsedTime, cn);
                System.out.println("  分区开始时间: " + startTime);
                System.out.println("  分区结束时间: " + endTime);
                System.out.println("  分区时间部分: " + timePart);
                
                // 4. 测试gene_code_mapping反转
                System.out.println("\n4. 测试gene_code_mapping反转:");
                JsonNode geneTransJson = JsonUtils.reverseGeneCodeMapping(geneCodeMapping);
                System.out.println("  原始映射: " + geneCodeMapping);
                System.out.println("  反转后映射: " + JsonUtils.toJsonString(geneTransJson));
                
                // 5. 测试main_ext构建
                System.out.println("\n5. 测试main_ext构建:");
                JsonNode mainExt = JsonUtils.buildMainExt(jsonNode);
                System.out.println("  Main Ext字段数量: " + mainExt.size());
                System.out.println("  Main Ext内容: " + JsonUtils.toJsonString(mainExt));
                
                // 6. 测试分流逻辑
                System.out.println("\n6. 测试分流逻辑:");
                boolean hasPointId = pointId != null && !pointId.trim().isEmpty();
                System.out.println("  是否有Point ID: " + hasPointId);
                System.out.println("  应该路由到: " + (hasPointId ? "正式表" : "中间表"));
                
                // 7. 测试分区名称计算
                System.out.println("\n7. 测试分区名称计算:");
                if (hasPointId) {
                    String partitionName = String.format("env_%s_gf_%s_%s", cn, pointType, timePart);
                    System.out.println("  正式表分区名: " + partitionName);
                } else {
                    String midTimePart = DateTimeUtils.calculateMidTablePartitionTimePart(parsedTime);
                    String partitionName = String.format("env_%s_gf_nomapping_%s", cn, midTimePart);
                    System.out.println("  中间表分区名: " + partitionName);
                }
                
                System.out.println("\n✓ 所有测试通过！");
                
            } else {
                System.out.println("✗ JSON解析失败");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 测试过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== JSON解析测试完成 ===");
    }
}

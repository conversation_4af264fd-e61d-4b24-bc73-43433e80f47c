package com.foshan.gf.test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.sql.Timestamp;

/**
 * Simple test for basic functionality
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        System.out.println("=== GF Data Processor Test ===");
        
        // Test time parsing
        testTimeParser();
        
        // Test partition calculation
        testPartitionCalculation();
        
        // Test data routing logic
        testDataRouting();
        
        System.out.println("\n=== Test Completed ===");
    }
    
    private static void testTimeParser() {
        System.out.println("\n1. Time Parsing Test:");
        
        String[] testTimes = {
            "**************",
            "**************", 
            "**************"
        };
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        
        for (String timeStr : testTimes) {
            try {
                LocalDateTime localTime = LocalDateTime.parse(timeStr, formatter);
                Timestamp timestamp = Timestamp.valueOf(localTime);
                System.out.println("  " + timeStr + " -> " + timestamp);
            } catch (Exception e) {
                System.out.println("  " + timeStr + " -> Parse failed: " + e.getMessage());
            }
        }
    }
    
    private static void testPartitionCalculation() {
        System.out.println("\n2. Partition Calculation Test:");
        
        String timeStr = "**************";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime localTime = LocalDateTime.parse(timeStr, formatter);
        
        String[] dataTypes = {"2051", "2061", "2031"};
        
        for (String dataType : dataTypes) {
            System.out.println("  Data Type: " + dataType);
            
            switch (dataType) {
                case "2051": // Minute data - monthly partition
                    String monthStart = String.format("%04d-%02d-01 00:00:00", 
                        localTime.getYear(), localTime.getMonthValue());
                    String monthEnd = String.format("%04d-%02d-01 00:00:00", 
                        localTime.plusMonths(1).getYear(), localTime.plusMonths(1).getMonthValue());
                    String monthPart = String.format("%04d%02d", localTime.getYear(), localTime.getMonthValue());
                    System.out.println("    Partition range: " + monthStart + " to " + monthEnd);
                    System.out.println("    Time part: " + monthPart);
                    break;
                    
                case "2061": // Hour data - yearly partition
                    String yearStart = String.format("%04d-01-01 00:00:00", localTime.getYear());
                    String yearEnd = String.format("%04d-01-01 00:00:00", localTime.getYear() + 1);
                    String yearPart = String.valueOf(localTime.getYear());
                    System.out.println("    Partition range: " + yearStart + " to " + yearEnd);
                    System.out.println("    Time part: " + yearPart);
                    break;
                    
                case "2031": // Day data - no time partition
                    System.out.println("    Partition range: 1970-01-01 00:00:00 to 2099-12-31 23:59:59");
                    System.out.println("    Time part: default");
                    break;
            }
        }
    }
    
    private static void testDataRouting() {
        System.out.println("\n3. Data Routing Test:");
        
        // Test point_id validation
        String[] pointIds = {"", "  ", null, "11674", "0"};
        
        for (String pointId : pointIds) {
            boolean hasPointId = pointId != null && !pointId.trim().isEmpty();
            String route = hasPointId ? "Main Table" : "Mid Table";
            System.out.println("  Point ID: '" + pointId + "' -> Route to: " + route);
        }
        
        // Test partition name generation
        System.out.println("\n  Partition Name Generation:");
        String dataType = "2061";
        String pointType = "31";
        String timePart = "2025";
        
        // Main table partition
        String mainPartition = String.format("env_%s_gf_%s_%s", dataType, pointType, timePart);
        System.out.println("    Main table partition: " + mainPartition);
        
        // Mid table partition
        String midPartition = String.format("env_%s_gf_nomapping_%s", dataType, "202507");
        System.out.println("    Mid table partition: " + midPartition);
        
        // Test data type routing
        System.out.println("\n  Data Type Routing:");
        String[] dataTypes = {"2051", "2061", "2031", "unknown"};
        for (String dt : dataTypes) {
            String description;
            switch (dt) {
                case "2051":
                    description = "Minute data";
                    break;
                case "2061":
                    description = "Hour data";
                    break;
                case "2031":
                    description = "Day data";
                    break;
                default:
                    description = "Unknown data type";
                    break;
            }
            System.out.println("    " + dt + " -> " + description);
        }
    }
}

package com.foshan.gf.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 日期时间工具类
 * 处理时间格式转换和分区时间计算
 */
public class DateTimeUtils {
    
    private static final Logger log = LoggerFactory.getLogger(DateTimeUtils.class);
    
    // 数据时间格式：yyyyMMddHHmmss
    private static final DateTimeFormatter DATA_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 将字符串时间转换为Timestamp
     * @param dataTimeStr 时间字符串，格式：yyyyMMddHHmmss
     * @return Timestamp对象，转换失败返回当前时间
     */
    public static Timestamp parseDataTime(String dataTimeStr) {
        if (dataTimeStr == null || dataTimeStr.trim().isEmpty()) {
            log.warn("Data time is null or empty, using current time");
            return new Timestamp(System.currentTimeMillis());
        }
        
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(dataTimeStr, DATA_TIME_FORMATTER);
            return Timestamp.valueOf(localDateTime);
        } catch (DateTimeParseException e) {
            log.error("Failed to parse data time: {}, using current time", dataTimeStr, e);
            return new Timestamp(System.currentTimeMillis());
        }
    }
    
    /**
     * 计算分区时间范围的开始时间
     * @param dataTime 数据时间
     * @param dataType 数据类型（2051/2061/2031）
     * @return 分区开始时间字符串
     */
    public static String calculatePartitionStartTime(Timestamp dataTime, String dataType) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        
        switch (dataType) {
            case "2051": // 分钟数据按月分区
                return String.format("%04d-%02d-01 00:00:00", 
                    localTime.getYear(), localTime.getMonthValue());
                
            case "2061": // 小时数据按年分区
                return String.format("%04d-01-01 00:00:00", localTime.getYear());
                
            case "2031": // 日数据不按时间分区
                return "1970-01-01 00:00:00";
                
            default:
                log.warn("Unknown data type: {}, using default partition", dataType);
                return "1970-01-01 00:00:00";
        }
    }
    
    /**
     * 计算分区时间范围的结束时间
     * @param dataTime 数据时间
     * @param dataType 数据类型（2051/2061/2031）
     * @return 分区结束时间字符串
     */
    public static String calculatePartitionEndTime(Timestamp dataTime, String dataType) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        
        switch (dataType) {
            case "2051": // 分钟数据按月分区
                LocalDateTime nextMonth = localTime.plusMonths(1);
                return String.format("%04d-%02d-01 00:00:00", 
                    nextMonth.getYear(), nextMonth.getMonthValue());
                
            case "2061": // 小时数据按年分区
                return String.format("%04d-01-01 00:00:00", localTime.getYear() + 1);
                
            case "2031": // 日数据不按时间分区
                return "2099-12-31 23:59:59";
                
            default:
                log.warn("Unknown data type: {}, using default partition", dataType);
                return "2099-12-31 23:59:59";
        }
    }
    
    /**
     * 计算中间表分区时间范围的开始时间（统一按月分区）
     * @param dataTime 数据时间
     * @return 分区开始时间字符串
     */
    public static String calculateMidTablePartitionStartTime(Timestamp dataTime) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        return String.format("%04d-%02d-01 00:00:00", 
            localTime.getYear(), localTime.getMonthValue());
    }
    
    /**
     * 计算中间表分区时间范围的结束时间（统一按月分区）
     * @param dataTime 数据时间
     * @return 分区结束时间字符串
     */
    public static String calculateMidTablePartitionEndTime(Timestamp dataTime) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        LocalDateTime nextMonth = localTime.plusMonths(1);
        return String.format("%04d-%02d-01 00:00:00", 
            nextMonth.getYear(), nextMonth.getMonthValue());
    }
    
    /**
     * 计算分区名称的时间部分
     * @param dataTime 数据时间
     * @param dataType 数据类型
     * @return 时间部分字符串
     */
    public static String calculatePartitionTimePart(Timestamp dataTime, String dataType) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        
        switch (dataType) {
            case "2051": // 分钟数据按月分区：202508
                return String.format("%04d%02d", localTime.getYear(), localTime.getMonthValue());
                
            case "2061": // 小时数据按年分区：2025
                return String.valueOf(localTime.getYear());
                
            case "2031": // 日数据不按时间分区：default
                return "default";
                
            default:
                log.warn("Unknown data type: {}, using default time part", dataType);
                return "default";
        }
    }
    
    /**
     * 计算中间表分区名称的时间部分（统一按月分区）
     * @param dataTime 数据时间
     * @return 时间部分字符串（格式：202508）
     */
    public static String calculateMidTablePartitionTimePart(Timestamp dataTime) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        return String.format("%04d%02d", localTime.getYear(), localTime.getMonthValue());
    }
}

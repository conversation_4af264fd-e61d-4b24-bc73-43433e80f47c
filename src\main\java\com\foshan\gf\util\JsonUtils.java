package com.foshan.gf.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;

/**
 * JSON工具类
 * 处理JSON解析、转换和构建
 */
public class JsonUtils {
    
    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 解析JSON字符串为JsonNode
     * @param jsonStr JSON字符串
     * @return JsonNode对象，解析失败返回null
     */
    public static JsonNode parseJson(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readTree(jsonStr);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON: {}", jsonStr, e);
            return null;
        }
    }
    
    /**
     * 将对象转换为JSON字符串
     * @param obj 要转换的对象
     * @return JSON字符串，转换失败返回null
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to JSON: {}", obj, e);
            return null;
        }
    }
    
    /**
     * 反转gene_code_mapping映射关系
     * 将{原码:新码}格式转换为{新码:原码}格式
     * @param geneCodeMappingStr 原始映射JSON字符串
     * @return 反转后的JsonNode，失败返回空对象
     */
    public static JsonNode reverseGeneCodeMapping(String geneCodeMappingStr) {
        ObjectNode result = objectMapper.createObjectNode();
        
        if (geneCodeMappingStr == null || geneCodeMappingStr.trim().isEmpty()) {
            return result;
        }
        
        try {
            JsonNode originalMapping = objectMapper.readTree(geneCodeMappingStr);
            if (originalMapping.isObject()) {
                Iterator<Map.Entry<String, JsonNode>> fields = originalMapping.fields();
                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> entry = fields.next();
                    String originalCode = entry.getKey();
                    String newCode = entry.getValue().asText();
                    result.put(newCode, originalCode);
                }
            }
        } catch (JsonProcessingException e) {
            log.error("Failed to reverse gene code mapping: {}", geneCodeMappingStr, e);
        }
        
        return result;
    }
    
    /**
     * 构建main_ext字段（排除核心字段后的JSON）
     * @param originalJson 原始JSON节点
     * @return 排除核心字段后的JsonNode
     */
    public static JsonNode buildMainExt(JsonNode originalJson) {
        if (originalJson == null || !originalJson.isObject()) {
            return objectMapper.createObjectNode();
        }
        
        ObjectNode mainExt = objectMapper.createObjectNode();
        
        // 需要排除的核心字段
        String[] excludeFields = {
            "id", "point_id", "point_type", "operation_customer_id", 
            "customer_id", "mn", "cn", "data_time", "gene_code_mapping", 
            "factors"
        };
        
        // 复制所有字段，排除核心字段
        Iterator<Map.Entry<String, JsonNode>> fields = originalJson.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String fieldName = entry.getKey();
            
            boolean shouldExclude = false;
            for (String excludeField : excludeFields) {
                if (excludeField.equals(fieldName)) {
                    shouldExclude = true;
                    break;
                }
            }
            
            if (!shouldExclude) {
                mainExt.set(fieldName, entry.getValue());
            }
        }
        
        return mainExt;
    }
    
    /**
     * 安全获取JSON节点的文本值
     * @param node JSON节点
     * @param fieldName 字段名
     * @return 字段值，不存在或为null返回null
     */
    public static String getTextValue(JsonNode node, String fieldName) {
        if (node == null || !node.has(fieldName)) {
            return null;
        }
        
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode.isNull()) {
            return null;
        }
        
        return fieldNode.asText();
    }
    
    /**
     * 安全获取JSON节点的文本值，带默认值
     * @param node JSON节点
     * @param fieldName 字段名
     * @param defaultValue 默认值
     * @return 字段值，不存在或为null返回默认值
     */
    public static String getTextValue(JsonNode node, String fieldName, String defaultValue) {
        String value = getTextValue(node, fieldName);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 安全获取JSON子节点
     * @param node JSON节点
     * @param fieldName 字段名
     * @return 子节点，不存在返回null
     */
    public static JsonNode getJsonNode(JsonNode node, String fieldName) {
        if (node == null || !node.has(fieldName)) {
            return null;
        }
        
        return node.get(fieldName);
    }
    
    /**
     * 检查字符串是否为空或null
     * @param str 字符串
     * @return true如果为空或null
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}

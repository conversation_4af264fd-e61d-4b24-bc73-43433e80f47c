# 国发数据接入系统配置文件

# Flink应用配置
gf.app.name=gf-data-processor
gf.app.version=1.0
gf.app.parallelism=2

# Kafka配置
gf.kafka.bootstrap-servers=10.118.1.209:9296,10.118.1.210:9296,10.118.1.211:9296
gf.kafka.topic=t212_gf
gf.kafka.group-id=gf-data-processor
gf.kafka.max-poll-records=5000
gf.kafka.fetch-max-bytes=52428800
gf.kafka.auto-offset-reset=latest
gf.kafka.enable-auto-commit=false
gf.kafka.isolation-level=read_committed

# PostgreSQL配置
gf.postgres.url=******************************************
gf.postgres.username=env_root
gf.postgres.password=env_root@fs_2020
gf.postgres.batch-size=2000
gf.postgres.batch-interval-ms=3000
gf.postgres.max-retries=3
gf.postgres.connection-timeout=30000

# 分区管理配置
gf.partition.cache-size=1000
gf.partition.cache-expire-hours=1
gf.partition.check-interval-seconds=300

# 监控配置
gf.metrics.enabled=true
gf.metrics.report-interval-seconds=30

# 日志配置
logging.level.com.foshan.gf=INFO
logging.level.org.apache.flink=WARN
logging.level.org.apache.kafka=WARN

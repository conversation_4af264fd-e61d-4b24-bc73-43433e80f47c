//package com.foshan.gf;
//
//import com.foshan.gf.model.GFDataRecord;
//import com.foshan.gf.source.GFJsonDeserializer;
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.IOException;
//
///**
// * JSON解析测试类
// * 使用设计方案中提供的测试数据验证JSON解析功能
// */
//public class JsonParsingTest {
//
//    private static final Logger log = LoggerFactory.getLogger(JsonParsingTest.class);
//
//    // 测试数据1：point_id为空的数据（应该路由到中间表）
//    private static final String TEST_JSON_1 =
//        "{\"id\":\"2744f1b1a60046cb8a7d45cb8ef09ff2\",\"customer_id\":\"1674586959261044737\",\"mn\":\"20160930A00013\",\"point_id\":\"\",\"point_type\":\"\",\"operation_customer_id\":\"\",\"gene_code_mapping\":\"\",\"pt_mn\":\"\",\"data_pwd\":\"\",\"outlet_code\":\"\",\"cn\":\"\",\"data_time\":\"20250730140000\",\"workcordSc\":\"\",\"zd_workcordSc\":\"\",\"zd_workcordZl\":\"\",\"workcordZl\":\"\",\"rg_workcordSc\":\"\",\"rg_workcordZl\":\"\",\"log_time\":\"\",\"factors\":{\"01\":{\"factor_code\":\"01\",\"rtd_value\":\"\",\"avg_value\":\"101.28\",\"min_value\":\"\",\"max_value\":\"\",\"cou_value\":\"\",\"avg_hm_revised\":\"\",\"avg_revised\":\"\",\"cou_zs_revised\":\"\",\"rtd_zs_value\":\"\",\"avg_zs_value\":\"\",\"min_zs_value\":\"\",\"max_zs_value\":\"\",\"cou_zs_value\":\"\",\"avg_zs_hm_revised\":\"\",\"avg_zs_revised\":\"\",\"flag\":\"\",\"zd_flag\":\"\",\"rg_flag\":\"\",\"value_revised\":\"\",\"cou_value_revised\":\"\",\"outlet_standard\":\"\",\"outlet_standard_cou\":\"\",\"zd_data_valid\":\"\",\"rg_data_valid\":\"\",\"value_unit\":\"\",\"cou_value_unit\":\"\"},\"02\":{\"factor_code\":\"02\",\"rtd_value\":\"\",\"avg_value\":\"190.34\",\"min_value\":\"\",\"max_value\":\"\",\"cou_value\":\"\",\"avg_hm_revised\":\"\",\"avg_revised\":\"\",\"cou_zs_revised\":\"\",\"rtd_zs_value\":\"\",\"avg_zs_value\":\"\",\"min_zs_value\":\"\",\"max_zs_value\":\"\",\"cou_zs_value\":\"\",\"avg_zs_hm_revised\":\"\",\"avg_zs_revised\":\"\",\"flag\":\"\",\"zd_flag\":\"\",\"rg_flag\":\"\",\"value_revised\":\"\",\"cou_value_revised\":\"\",\"outlet_standard\":\"\",\"outlet_standard_cou\":\"\",\"zd_data_valid\":\"\",\"rg_data_valid\":\"\",\"value_unit\":\"\",\"cou_value_unit\":\"\"}}}\";
//
//    // 测试数据2：point_id非空的数据（应该路由到正式表）
//    private static final String TEST_JSON_2 = """
//        {"id":"38a81b6672ae439781c3860be1e4aad0","customer_id":"1674586959261044737","mn":"12315ctsy001","point_id":"11674","point_type":"31","operation_customer_id":"1674586959261044737","gene_code_mapping":"{\\"01\\": \\"NC147\\", \\"02\\": \\"NC50\\", \\"03\\": \\"NC123\\", \\"S01\\": \\"NC120\\", \\"S08\\": \\"NC149\\"}","pt_mn":"12315ctsy001","data_pwd":"12345611674","outlet_code":"","cn":"","data_time":"20250730140000","workcordSc":"","zd_workcordSc":"","zd_workcordZl":"","workcordZl":"","rg_workcordSc":"","rg_workcordZl":"","log_time":"","factors":{"01":{"factor_code":"01","rtd_value":"","avg_value":"113.29"},"02":{"factor_code":"02","rtd_value":"","avg_value":"178.05"}}}
//        """;
//
//    // 测试数据3：有cn字段的数据（小时数据）
//    private static final String TEST_JSON_3 = """
//        {"id":"","point_id":"111","point_type":"31","operation_customer_id":"11111544","customer_id":"11111544","gene_code_mapping":"{\\"011\\": \\"NC68\\", \\"065\\":\\"NC92\\", \\"101\\": \\"NC97\\", \\"B01\\": \\"NC137\\"}","mn":"20160930A00013","pwd":"654321","outlet_code":"","cn":"2061","data_time":"20250731000000","workcordSc":"","zd_workcordSc":"","zd_workcordZl":"","workcordZl":"","rg_workcordSc":"","rg_workcordZl":"","log_time":"","factors":{"011":{"factor_code":"011","rtd_value":"","avg_value":"97.38"},"101":{"factor_code":"101","rtd_value":"","avg_value":"198.53"}}}
//        """;
//
//    @Test
//    public void testJsonParsing() throws IOException {
//        GFJsonDeserializer deserializer = new GFJsonDeserializer();
//
//        log.info("=== Testing JSON Parsing ===");
//
//        // 测试数据1
//        log.info("Testing JSON 1 (point_id empty):");
//        GFDataRecord record1 = deserializer.deserialize(TEST_JSON_1.getBytes());
//        if (record1 != null) {
//            log.info("Record 1 - ID: {}, Point ID: '{}', Has Point ID: {}, MN: {}, Data Time: {}",
//                record1.getId(), record1.getPointId(), record1.hasPointId(),
//                record1.getMn(), record1.getDataTime());
//            log.info("Record 1 - Customer ID: {}, CN: {}, Parsed Time: {}",
//                record1.getCustomerId(), record1.getCn(), record1.getParsedDataTime());
//        } else {
//            log.error("Failed to parse JSON 1");
//        }
//
//        // 测试数据2
//        log.info("\nTesting JSON 2 (point_id non-empty):");
//        GFDataRecord record2 = deserializer.deserialize(TEST_JSON_2.getBytes());
//        if (record2 != null) {
//            log.info("Record 2 - ID: {}, Point ID: '{}', Has Point ID: {}, MN: {}, Data Time: {}",
//                record2.getId(), record2.getPointId(), record2.hasPointId(),
//                record2.getMn(), record2.getDataTime());
//            log.info("Record 2 - Point Type: {}, CN: {}, Gene Code Mapping: {}",
//                record2.getPointType(), record2.getCn(), record2.getGeneCodeMapping());
//        } else {
//            log.error("Failed to parse JSON 2");
//        }
//
//        // 测试数据3
//        log.info("\nTesting JSON 3 (with cn field):");
//        GFDataRecord record3 = deserializer.deserialize(TEST_JSON_3.getBytes());
//        if (record3 != null) {
//            log.info("Record 3 - ID: '{}', Point ID: '{}', Has Point ID: {}, MN: {}, Data Time: {}",
//                record3.getId(), record3.getPointId(), record3.hasPointId(),
//                record3.getMn(), record3.getDataTime());
//            log.info("Record 3 - CN: {}, Point Type: {}, Parsed Time: {}",
//                record3.getCn(), record3.getPointType(), record3.getParsedDataTime());
//        } else {
//            log.error("Failed to parse JSON 3");
//        }
//
//        log.info("=== JSON Parsing Test Completed ===");
//    }
//}

# 日志配置文件

# 根Logger配置
rootLogger.level = INFO
rootLogger.appenderRef.console.ref = ConsoleAppender
rootLogger.appenderRef.file.ref = FileAppender

# 控制台输出配置
appender.console.type = Console
appender.console.name = ConsoleAppender
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%t] %c{1}:%L - %m%n

# 文件输出配置
appender.file.type = RollingFile
appender.file.name = FileAppender
appender.file.fileName = logs/gf-data-processor.log
appender.file.filePattern = logs/gf-data-processor-%d{yyyy-MM-dd}-%i.log.gz
appender.file.layout.type = PatternLayout
appender.file.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%t] %c{1}:%L - %m%n
appender.file.policies.type = Policies
appender.file.policies.time.type = TimeBasedTriggeringPolicy
appender.file.policies.time.interval = 1
appender.file.policies.time.modulate = true
appender.file.policies.size.type = SizeBasedTriggeringPolicy
appender.file.policies.size.size = 100MB
appender.file.strategy.type = DefaultRolloverStrategy
appender.file.strategy.max = 10

# 特定包的日志级别
logger.gf.name = com.foshan.gf
logger.gf.level = INFO
logger.gf.additivity = false
logger.gf.appenderRef.console.ref = ConsoleAppender
logger.gf.appenderRef.file.ref = FileAppender

logger.flink.name = org.apache.flink
logger.flink.level = WARN

logger.kafka.name = org.apache.kafka
logger.kafka.level = WARN

logger.postgres.name = org.postgresql
logger.postgres.level = WARN

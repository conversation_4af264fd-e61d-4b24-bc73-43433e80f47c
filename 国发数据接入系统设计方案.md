# 国发数据接入系统设计方案

## 文档信息

| 项目名称 | 国发数据接入系统 |
|---------|-----------------|
| 版本号   | v1.0           |
| 创建日期 | 2025-08-28     |
| 技术栈   | Flink 1.17.2 + PostgreSQL 12 + Kafka 2.8 |
| 运行模式 | Flink Standalone |

## 1. 项目概述

### 1.1 项目背景
本项目旨在构建一个高性能的实时数据处理系统，用于接入佛山市局的国发数据。系统需要从Kafka消费国发数据，经过实时处理后，根据业务规则分别写入PostgreSQL的不同分区表中。

### 1.2 业务需求
- 实时消费Kafka topic `t212_gf`的国发数据
- 数据格式为JSON，每秒处理约10,000条记录
- **多表分离**: 根据`data_type`将数据写入不同PG表(env_2051_gf/env_2061_gf/env_2031_gf)
- **二级分流**: 根据`point_id`字段是否为空进行数据分流
- **历史数据适配**: 支持Kafka传输历史数据补录，时间乱序不确定
- 数据写入PostgreSQL的分区表中
- 支持动态分区管理和高可用性

### 1.3 技术约束
- **Apache Flink**: 1.17.2版本，Standalone集群模式
- **PostgreSQL**: 12版本，支持分区表
- **Apache Kafka**: 2.8版本
- **并行度**: 1-2（低并行度高吞吐场景）
- **数据量**: 每秒10,000条记录

## 2. 系统架构设计

**国发数据接入系统整体流程图**:

```mermaid
flowchart TD
    A["国发数据源<br/>Kafka主题: t212_gf"] --> B["数据时间提取<br/>用于分区检测"]
    
    B --> C["按数据类型分流"]
    
    C --> D1["分钟数据<br/>2051"]
    C --> D2["小时数据<br/>2061"]
    C --> D3["日数据<br/>2031"]
    
    D1 --> E1{"站点标识<br/>非空判断"}
    D2 --> E2{"站点标识<br/>非空判断"}
    D3 --> E3{"站点标识<br/>非空判断"}
    
    E1 -->|"站点标识为空"| F1["分钟中间表<br/>env_2051_gf_nomapping"]
    E1 -->|"站点标识非空"| G1["分钟正式表<br/>env_2051_gf"]
    
    E2 -->|"站点标识为空"| F2["小时中间表<br/>env_2061_gf_nomapping"]
    E2 -->|"站点标识非空"| G2["小时正式表<br/>env_2061_gf"]
    
    E3 -->|"站点标识为空"| F3["日中间表<br/>env_2031_gf_nomapping"]
    E3 -->|"站点标识非空"| G3["日正式表<br/>env_2031_gf"]
    
    style A fill:#e3f2fd
    style C fill:#fff8e1
    style E1 fill:#fff8e1
    style E2 fill:#fff8e1
    style E3 fill:#fff8e1
    style F1 fill:#fff3e0
    style F2 fill:#fff3e0
    style F3 fill:#fff3e0
    style G1 fill:#e8f5e8
    style G2 fill:#e8f5e8
    style G3 fill:#e8f5e8
```

**数据分流处理架构**:

```mermaid
graph 
    A["Kafka数据源"] --> B["JSON解析验证"]
    B --> C["数据类型分流器"]
    
    C --> D1["2051分钟数据流"]
    C --> D2["2061小时数据流"]
    C --> D3["2031日数据流"]
    
    D1 --> E1["站点标识分流器"]
    D2 --> E2["站点标识分流器"]
    D3 --> E3["站点标识分流器"]
    
    E1 --> F1["正式表字段映射"]
    E1 --> F2["中间表字段映射"]
    
    E2 --> F3["正式表字段映射"]
    E2 --> F4["中间表字段映射"]
    
    E3 --> F5["正式表字段映射"]
    E3 --> F6["中间表字段映射"]
    
    F1 --> G1["env_2051_gf分区表"]
    F2 --> G2["env_2051_gf_nomapping分区表"]
    F3 --> G3["env_2061_gf分区表"]
    F4 --> G4["env_2061_gf_nomapping分区表"]
    F5 --> G5["env_2031_gf分区表"]
    F6 --> G6["env_2031_gf_nomapping分区表"]
    
    style A fill:#e3f2fd
    style C fill:#fff8e1
    style E1 fill:#fff8e1
    style E2 fill:#fff8e1
    style E3 fill:#fff8e1
    style G1 fill:#e8f5e8
    style G2 fill:#e8f5e8
    style G3 fill:#e8f5e8
    style G4 fill:#e8f5e8
    style G5 fill:#e8f5e8
    style G6 fill:#e8f5e8
```

**技术组件交互流程**:

```mermaid
sequenceDiagram
    participant K as Kafka消息队列
    participant F as Flink流式引擎
    participant P as 分区管理器
    participant D as PostgreSQL数据库
    participant M as 监控系统
    
    K->>F: 接收国发数据消息
    Note over F: JSON解析和数据验证
    
    F->>F: 第一级分流（数据类型）
    F->>F: 第二级分流（站点标识）
    F->>F: 字段映射和数据转换
    
    F->>P: 检查分区表是否存在
    
    alt 分区表不存在
        P->>D: 创建新的分区表
        D-->>P: 返回创建结果
        P-->>F: 确认分区就绪
    else 分区表存在
        P-->>F: 确认分区就绪
    end
    
    F->>D: 批量写入数据到分区表
    D-->>F: 返回写入结果
    
    F->>M: 上报处理指标和状态
    
    Note over F,D: 实时数据处理完成
```



**架构特点**:
1. **无水位线模式**: 禁用传统水位线，适应历史数据补录的不确定时间
2. **二级分流策略**: 先按data_type分离到不同表，再按point_id分离到正式表/中间表
3. **多表并行**: 不同data_type的数据流独立并行处理
4. **实时分区检测**: 每条记录根据实际data_time检测对应分区是否存在

### 2.3 核心组件说明

| 组件名称 | 功能描述 | 关键特性 |
|---------|----------|----------|
| Source层 | Kafka数据消费 | 批量拉取、背压控制、无水位线模式 |
| Process层 | 数据处理转换 | 二级分流、字段映射、JSON转换 |
| 分区检测层 | 动态分区管理 | 实时检测、多级缓存、历史数据适配 |
| Sink层 | 数据库写入 | 批量写入、异步IO、多表并行 |
| 监控层 | 系统监控告警 | 性能指标、异常检测、分区状态 |

## 3. 技术实现设计

### 3.1 Flink源端配置

```
# 应用基础配置
gf.app.name=gf-data-processor
gf.app.version=1.0
gf.app.parallelism=2
``` 

### 3.2 Kafka源端配置

#### 3.2.1 消费者配置
```properties
# 高吞吐优化
fetch.max.bytes: 52428800
max.poll.records: 5000
receive.buffer.bytes: 65536
fetch.min.bytes: 1024
fetch.max.wait.ms: 100

# 可靠性配置
enable.auto.commit: false
isolation.level: read_committed
```

#### 3.2.2 数据格式定义
```json
{
  "id": "唯一标识",
  "point_id": "站点ID（可为空）",
  "point_type": "站点类型",
  "operation_customer_id": "运维企业ID", 
  "mn": "设备MN码",
  "cn": "数据类型（2051/2061/2031）",
  "data_time": "数据时间yyyyMMddHHmmss",
  "gene_code_mapping": "因子编码映射JSON",
  "factors": "因子监测数据JSON",
  "其他字段": "排除核心字段后的JSON,如customer_id、pwd、outlet_code、workcordSc、zd_workcordSc、zd_workcordZl、workcordZl、rg_workcordSc、rg_workcordZl"
}
```

### 3.3 数据处理逻辑

#### 3.3.1 数据预处理流程(适应历史数据)
1. **JSON解析**: 使用Flink的JSON反序列化器
2. **数据验证**: 检查必要字段完整性
3. **时间转换**: data_time从字符串转为timestamp(用于分区检测)
4. **无水位线模式**: 禁用水位线，适应历史数据乱序场景
5. **实时分区检测**: 每条记录根据实际data_time动态检测对应分区

#### 3.3.2 数据分流策略(二级分流)
```java
// 第一级分流: 按data_type分离到不同表
switch (record.cn) {
    case "2051": // 分钟数据
        ctx.output(minuteDataSideOutput, record);
        break;
    case "2061": // 小时数据
        ctx.output(hourDataSideOutput, record);
        break;
    case "2031": // 日数据
        ctx.output(dayDataSideOutput, record);
        break;
    default:
        log.warn("Unknown data_type: {}", record.cn);
}

// 第二级分流: 在每个data_type流中按point_id分离
if (record.point_id != null && !record.point_id.isEmpty()) {
    // 路由到正式表处理链: env_{data_type}_gf
    return "main_table_stream";
} else {
    // 路由到中间表处理链: env_{data_type}_gf_nomapping
    return "mid_table_stream";
}
```

#### 3.3.3 字段映射规则

**正式表（env_{data_type}_gf - point_id非空）字段映射**:

| 源字段 | 目标字段 | 转换规则 | 备注 |
|--------|----------|----------|------|
| point_id | point_id | 直接映射 | 必须非空 |
| data_time | data_time | 格式转换为timestamp | yyyyMMddHHmmss→timestamp |
| cn | data_type | 直接映射 | 2051/2061/2031 |
| id | qn | 直接映射 | 请求编码 |
| mn | mncode | 直接映射 | 设备MN码 |
| factors | gene_json | JSON直接存储 | 因子监测数据 |
| 其他字段 | main_ext | 排除核心字段后的JSON | 如customer_id、pwd、outlet_code、workcordSc、zd_workcordSc、zd_workcordZl、workcordZl、rg_workcordSc、rg_workcordZl |
| point_type | point_type | 直接映射 | 站点类型 |
| - | write_time | 当前时间 | now() |
| - | source | 固定值"nt" | 数据来源标识 |
| operation_customer_id | operation_customer_id | 直接映射 | 运维企业ID |
| gene_code_mapping | gene_trans_json | 映射关系反转 | {新码:原码}格式 |

**中间表（env_{data_type}_gf_nomapping - point_id为空）字段映射**:

| 源字段 | 目标字段 | 转换规则 | 备注 |
|--------|----------|----------|------|
| customer_id | customer_id | 直接映射 | 客户编码（主键之一） |
| mn | mncode | 直接映射 | 设备MN码（主键之一） |
| data_time | data_time | 格式转换为timestamp | 主键之一 |
| cn | data_type | 直接映射 | 数据类型 |
| id | qn | 直接映射 | 请求编码 |
| factors | gene_json | JSON直接存储 | 因子监测数据 |
| 其他字段 | main_ext | 排除核心字段后的JSON | 如customer_id、pwd、outlet_code、workcordSc、zd_workcordSc、zd_workcordZl、workcordZl、rg_workcordSc、rg_workcordZl |
| - | write_time | 当前时间 | now() |

### 3.4 PostgreSQL分区表设计

#### 3.4.1 分区策略

**正式表（env_{data_type}_gf）分区规则**:
- **一级分区**: 按point_type进行LIST分区
- **二级分区**: 按data_time进行RANGE分区
  - 2051（分钟数据）: 按月分区
  - 2061（小时数据）: 按年分区  
  - 2031（日数据）: 不做时间分区
- **主键**: (point_id, data_time, operation_customer_id, data_type, source)

**中间表（env_{data_type}_gf_nomapping）分区规则**:
- **单级分区**: 按data_time进行RANGE分区（按月）
- **主键**: (customer_id, data_time, mncode)

#### 3.4.2 分区命名规范

**正式表分区命名**:
```sql
-- 父表
env_{data_type}_gf  (如: env_2061_gf)

-- 一级分区（按point_type）
env_{data_type}_gf_{point_type}  (如: env_2061_gf_31)

-- 二级分区（按时间）
env_{data_type}_gf_{point_type}_{time_period}
-- 示例:
-- 小时数据按年: env_2061_gf_31_2025
-- 分钟数据按月: env_2051_gf_31_202508
```

**中间表分区命名**:
```sql
-- 父表
env_{data_type}_gf_nomapping  (如: env_2061_gf_nomapping)

-- 按月分区
env_{data_type}_gf_nomapping_{YYYYMM}  (如: env_2061_gf_nomapping_202508)
```

#### 3.4.3 索引策略

**正式表（env_{data_type}_gf）索引**:
```sql
-- 主键索引（复合主键）
PRIMARY KEY (point_id, data_time, operation_customer_id, data_type, source)

-- 时间查询索引
CREATE INDEX idx_env_{data_type}_gf_{point_type}_dt ON {table_name} USING btree (data_time);

```

**中间表（env_{data_type}_gf_nomapping）索引**:
```sql
-- 主键索引
PRIMARY KEY (customer_id,data_time, mncode)

```

### 3.5 动态分区管理

#### 3.5.1 分区检测逻辑
1. 根据data_type确定表名前缀
2. 根据point_type和data_time计算分区名
3. 检查分区是否存在
4. 如不存在，动态创建分区表和索引

#### 3.5.2 分区创建模板
```sql
-- 创建分区表模板
CREATE TABLE {partition_name} PARTITION OF {parent_table} 
FOR VALUES FROM ('{start_time}') TO ('{end_time}');

-- 创建主键约束
ALTER TABLE {partition_name} 
ADD CONSTRAINT {partition_name}_pkey PRIMARY KEY ({pk_columns});

-- 创建索引
CREATE INDEX {index_name} ON {partition_name} USING btree (data_time);
```

-- 缓存策略的具体实现和缓存命中率
```java
public class PartitionManager {
    
    private final Connection connection;
    private final Cache<String, Boolean> partitionCache;
    private final Map<String, String> partitionTemplates;
    
    public PartitionManager(Connection connection) {
        this.connection = connection;
        this.partitionCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
        this.partitionTemplates = buildPartitionTemplates();
    }
    
    public boolean ensurePartitionExists(String dataType, String pointType, 
                                       Timestamp dataTime, boolean isMainTable) {
        String partitionName = calculatePartitionName(dataType, pointType, dataTime, isMainTable);
        
        // 先查缓存
        Boolean exists = partitionCache.getIfPresent(partitionName);
        if (exists != null && exists) {
            return true;
        }
        
        // 检查分区是否存在
        if (checkPartitionExists(partitionName)) {
            partitionCache.put(partitionName, true);
            return true;
        }
        
        // 创建分区
        return createPartition(dataType, pointType, dataTime, isMainTable, partitionName);
    }
    
    private boolean checkPartitionExists(String partitionName) {
        String sql = """
            SELECT COUNT(1) FROM information_schema.tables 
            WHERE table_schema = 'env' 
            AND table_name = ?
            """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, partitionName);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            log.error("Failed to check partition existence: {}", partitionName, e);
        }
        return false;
    }
    
    private boolean createPartition(String dataType, String pointType, 
                                  Timestamp dataTime, boolean isMainTable, String partitionName) {
        try {
            String[] sqls = buildPartitionSqls(dataType, pointType, dataTime, 
                                             isMainTable, partitionName);
            
            connection.setAutoCommit(false);
            
            for (String sql : sqls) {
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.executeUpdate();
                    log.info("Created partition with SQL: {}", sql);
                }
            }
            
            connection.commit();
            partitionCache.put(partitionName, true);
            log.info("Successfully created partition: {}", partitionName);
            return true;
            
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException rollbackEx) {
                log.error("Failed to rollback partition creation", rollbackEx);
            }
            log.error("Failed to create partition: {}", partitionName, e);
            return false;
        } finally {
            try {
                connection.setAutoCommit(true);
            } catch (SQLException e) {
                log.error("Failed to reset auto-commit", e);
            }
        }
    }
    
    private String[] buildPartitionSqls(String dataType, String pointType, 
                                      Timestamp dataTime, boolean isMainTable, 
                                      String partitionName) {
        if (isMainTable) {
            return buildMainTablePartitionSqls(dataType, pointType, dataTime, partitionName);
        } else {
            return buildMidTablePartitionSqls(dataType, dataTime, partitionName);
        }
    }
    
    private String[] buildMainTablePartitionSqls(String dataType, String pointType, 
                                               Timestamp dataTime, String partitionName) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        
        // 根据data_type确定分区范围
        String startTime, endTime;
        switch (dataType) {
            case "2051": // 分钟数据按月分区
                startTime = String.format("%04d-%02d-01 00:00:00", 
                    localTime.getYear(), localTime.getMonthValue());
                endTime = String.format("%04d-%02d-01 00:00:00", 
                    localTime.plusMonths(1).getYear(), localTime.plusMonths(1).getMonthValue());
                break;
            case "2061": // 小时数据按年分区
                startTime = String.format("%04d-01-01 00:00:00", localTime.getYear());
                endTime = String.format("%04d-01-01 00:00:00", localTime.getYear() + 1);
                break;
            case "2031": // 日数据不按时间分区，创建默认分区
                startTime = "1970-01-01 00:00:00";
                endTime = "2099-12-31 23:59:59";
                break;
            default:
                throw new IllegalArgumentException("Unknown data_type: " + dataType);
        }
        
        String parentTable = String.format("env_%s_gf_%s", dataType, pointType);
        String pkColumns = "point_id, data_time, operation_customer_id, data_type, source";
        String indexName = String.format("idx_%s_dt", partitionName);
        
        return new String[] {
            // 创建分区表
            String.format("""
                CREATE TABLE env.%s PARTITION OF env.env_%s_gf_%s (
                    CONSTRAINT %s_pkey PRIMARY KEY (%s)
                ) FOR VALUES FROM ('%s') TO ('%s')
                """, partitionName, dataType, pointType, partitionName, pkColumns, startTime, endTime),
            
            // 创建时间索引
            String.format("CREATE INDEX %s ON env.%s USING btree (data_time)", 
                indexName, partitionName)
        };
    }
    
    private String[] buildMidTablePartitionSqls(String dataType, Timestamp dataTime, 
                                              String partitionName) {
        LocalDateTime localTime = dataTime.toLocalDateTime();
        
        // 中间表统一按月分区
        String startTime = String.format("%04d-%02d-01 00:00:00", 
            localTime.getYear(), localTime.getMonthValue());
        String endTime = String.format("%04d-%02d-01 00:00:00", 
            localTime.plusMonths(1).getYear(), localTime.plusMonths(1).getMonthValue());
        
        String pkColumns = "data_time, mncode";
        
        return new String[] {
            // 创建分区表
            String.format("""
                CREATE TABLE env.%s PARTITION OF env.env_%s_gf_nomapping (
                    CONSTRAINT %s_pkey PRIMARY KEY (%s)
                ) FOR VALUES FROM ('%s') TO ('%s')
                """, partitionName, dataType, partitionName, pkColumns, startTime, endTime)
        };
    }
}
```

## 4. 工程代码布局设计

### 4.1 项目整体结构

```
gf-data-processor/
├── pom.xml                              # Maven项目配置文件
├── README.md                            # 项目说明文档
├── docker-compose.yml                   # Docker容器编排
├── scripts/                             # 脚本文件目录
│   ├── build.sh                        # 构建脚本
│   ├── deploy.sh                       # 部署脚本
│   ├── start-cluster.sh                # Flink集群启动脚本
│   └── submit-job.sh                   # 作业提交脚本
├── conf/                               # 配置文件目录
│   ├── flink-conf.yaml                # Flink集群配置
│   ├── log4j2.properties              # 日志配置
│   ├── application.properties          # 应用配置
│   └── sql/                           # SQL脚本目录
│       ├── create_tables.sql          # 建表脚本
│       └── create_partitions.sql      # 分区创建脚本
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── foshan/
│       │           └── gf/
│       │               ├── GFDataProcessorJob.java        # 主程序入口
│       │               ├── config/                        # 配置管理
│       │               │   ├── AppConfig.java
│       │               │   ├── KafkaConfig.java
│       │               │   └── PostgreSQLConfig.java
│       │               ├── model/                         # 数据模型
│       │               │   ├── GFDataRecord.java
│       │               │   ├── MainTableRecord.java
│       │               │   └── MidTableRecord.java
│       │               ├── source/                        # Source层
│       │               │   ├── GFKafkaSource.java
│       │               │   └── GFJsonDeserializer.java
│       │               ├── process/                       # Process层
│       │               │   ├── GFJsonParser.java
│       │               │   ├── DataSplitProcessFunction.java
│       │               │   ├── PointIdSplitFunction.java
│       │               │   ├── MainTableMapper.java
│       │               │   └── MidTableMapper.java
│       │               ├── sink/                          # Sink层
│       │               │   ├── PostgreSQLSinkBuilder.java
│       │               │   ├── MainTableSinkFunction.java
│       │               │   └── MidTableSinkFunction.java
│       │               ├── partition/                     # 分区管理
│       │               │   ├── PartitionManager.java
│       │               │   ├── PartitionDetector.java
│       │               │   └── PartitionCreator.java
│       │               ├── metrics/                       # 监控指标
│       │               │   ├── BusinessMetrics.java
│       │               │   └── SystemMetrics.java
│       │               ├── util/                          # 工具类
│       │               │   ├── DateTimeUtils.java
│       │               │   ├── JsonUtils.java
│       │               │   └── ValidationUtils.java
│       │               └── exception/                     # 异常处理
│       │                   ├── GFProcessingException.java
│       │                   └── PartitionException.java
│       └── resources/
│           ├── application.properties                      # 应用配置
│           ├── log4j2.properties                         # 日志配置
│           └── META-INF/
│               └── services/
│                   └── org.apache.flink.table.factories.Factory
└── target/                                                # 编译输出目录
    └── gf-data-processor-1.0.jar                         # 打包后的JAR文件
```

### 4.2 核心模块说明

#### 4.2.1 主程序入口模块

**GFDataProcessorJob.java**
- **功能**: Flink作业的主入口，负责环境初始化和数据流构建
- **关键职责**:
  - 参数解析和配置加载
  - StreamExecutionEnvironment环境配置
  - 检查点和重启策略配置
  - 数据处理链路构建
  - 作业提交和执行

#### 4.2.2 配置管理模块 (config/)

**KafkaConfig.java**
```java
@ConfigurationProperties(prefix = "gf.kafka")
public class KafkaConfig {
    private String bootstrapServers = "localhost:9092";
    private String topic = "t212_gf";
    private String groupId = "gf-data-processor";
    private int maxPollRecords = 5000;
    // getter/setter methods
}
```

**PostgreSQLConfig.java**
```java
@ConfigurationProperties(prefix = "gf.postgres")
public class PostgreSQLConfig {
    private String url = "****************************************";
    private String username = "flink_user";
    private String password = "password";
    private int batchSize = 2000;
    private long batchIntervalMs = 3000;
    // getter/setter methods
}
```

#### 4.2.3 数据模型模块 (model/)

**GFDataRecord.java**
```java
public class GFDataRecord {
    private String id;
    private String pointId;
    private String pointType;
    private String operationCustomerId;
    private String mn;
    private String cn;  // data_type
    private String dataTime;
    private String geneCodeMapping;
    private JsonNode factors;
    private String rawJson;
    private long parsedDataTime;
    private String partitionKey;
    // getter/setter methods
}
```

**MainTableRecord.java**
```java
public class MainTableRecord {
    private Integer pointId;
    private Timestamp dataTime;
    private String dataType;
    private String qn;
    private String mncode;
    private JsonNode geneJson;
    private JsonNode mainExt;
    private String pointType;
    private Timestamp writeTime;
    private String source;
    private String operationCustomerId;
    private JsonNode geneTransJson;
    // getter/setter methods
}
```

#### 4.2.4 Source层模块 (source/)

**GFKafkaSource.java**
- **功能**: Kafka数据源配置和创建
- **特性**: 高吞吐优化、无水位线模式、背压控制

**GFJsonDeserializer.java**
- **功能**: JSON反序列化器
- **特性**: 错误处理、性能优化、格式验证

#### 4.2.5 Process层模块 (process/)

**GFJsonParser.java**
- **功能**: JSON解析和数据验证
- **输入**: Kafka原始JSON字符串
- **输出**: GFDataRecord对象
- **特性**: 异常处理、指标统计、字段验证

**DataSplitProcessFunction.java**
- **功能**: 第一级数据分流（按data_type）
- **分流规则**: 2051→分钟数据, 2061→小时数据, 2031→日数据
- **输出**: 三个SideOutput流

**PointIdSplitFunction.java**
- **功能**: 第二级数据分流（按point_id）
- **分流规则**: point_id非空→正式表, point_id为空→中间表
- **输出**: 主流和中间表SideOutput

**MainTableMapper.java / MidTableMapper.java**
- **功能**: 字段映射和数据转换
- **特性**: 字段排除逻辑、JSON构建、类型转换

#### 4.2.6 Sink层模块 (sink/)

**PostgreSQLSinkBuilder.java**
- **功能**: PostgreSQL Sink构建器
- **特性**: 连接池管理、批量写入、UPSERT策略

```java
    // 正式表Sink
    public static SinkFunction<MainTableRecord> buildMainTableSink(String dataType) {
        String insertSql = String.format("""
            INSERT INTO env.env_%s_gf (
                point_id, data_time, data_type, qn, mncode, gene_json,
                main_ext, point_type, write_time, source, operation_customer_id,
                gene_trans_json
            ) VALUES (?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?, ?, ?, ?, ?::jsonb)
            ON CONFLICT (point_id, data_time, operation_customer_id, data_type, source)
            DO UPDATE SET
                gene_json = EXCLUDED.gene_json,
                main_ext = EXCLUDED.main_ext,
                write_time = EXCLUDED.write_time,
                gene_trans_json = EXCLUDED.gene_trans_json
            """, dataType);
        
        return JdbcSink.sink(
            insertSql,
            new MainTableRecordPreparedStatementSetter(dataType),
            JDBCConnectionProvider.getExecutionOptions(),
            JDBCConnectionProvider.getConnectionOptions()
        );
    }
    ``` 


**MainTableSinkFunction.java / MidTableSinkFunction.java**
- **功能**: 数据库写入函数
- **特性**: 分区检测、PreparedStatement、异常重试

```java
    // 中间表Sink
    public static SinkFunction<MidTableRecord> buildMidTableSink(String dataType) {
        String insertSql = String.format("""
            INSERT INTO env.env_%s_gf_nomapping (
                mncode, data_time, data_type, qn, gene_json, main_ext, write_time
            ) VALUES (?, ?, ?, ?, ?::jsonb, ?::jsonb, ?)
            ON CONFLICT (data_time, mncode)
            DO UPDATE SET
                gene_json = EXCLUDED.gene_json,
                main_ext = EXCLUDED.main_ext,
                write_time = EXCLUDED.write_time
            """, dataType);
        
        return JdbcSink.sink(
            insertSql,
            new MidTableRecordPreparedStatementSetter(dataType),
            JDBCConnectionProvider.getExecutionOptions(),
            JDBCConnectionProvider.getConnectionOptions()
        );
    }
```


#### 4.2.7 分区管理模块 (partition/)

**PartitionManager.java**
- **功能**: 分区管理核心类
- **特性**: 多级缓存、动态创建、并发安全

**PartitionDetector.java**
- **功能**: 分区存在检测
- **特性**: SQL查询、缓存机制、性能优化

**PartitionCreator.java**
- **功能**: 分区自动创建
- **特性**: SQL模板、事务管理、异常处理

#### 4.2.8 监控指标模块 (metrics/)

**BusinessMetrics.java**
```java
public class BusinessMetrics {
    private Counter recordsProcessed;
    private Counter recordsWritten;
    private Counter processingErrors;
    private Histogram processingLatency;
    private Gauge<Long> partitionCacheSize;
    // 指标注册和更新方法
}
```

**SystemMetrics.java**
- **功能**: 系统级指标监控
- **监控内容**: JVM内存、CPU使用率、网络IO、检查点时间

### 4.3 依赖管理 (pom.xml)

```xml
<project>
    <groupId>com.foshan.gf</groupId>
    <artifactId>gf-data-processor</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <flink.version>1.17.2</flink.version>
        <kafka.version>2.8.2</kafka.version>
        <postgresql.version>42.5.4</postgresql.version>
        <jackson.version>2.12.6</jackson.version>
        <flink.jdbc.version>3.1.1-1.17</flink.jdbc.version>
    </properties>
    
    <dependencies>
        <!-- Flink 核心依赖 -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java</artifactId>
            <version>${flink.version}</version>
        </dependency>
        
        <!-- Kafka连接器 -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-kafka</artifactId>
            <version>${flink.version}</version>
        </dependency>
        
        <!-- JDBC连接器 -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-jdbc</artifactId>
            <version>${flink.jdbc.version}</version>
        </dependency>
        
        <!-- PostgreSQL驱动 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>
        
        <!-- JSON处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        
        <!-- 日志框架 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <!-- Maven Shade插件，用于打包Fat JAR -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.foshan.gf.GFDataProcessorJob</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
```

### 4.4 配置文件模板

#### 4.4.1 应用配置 (application.properties)

```properties
# Flink应用配置
# Kafka配置
gf.kafka.bootstrap-servers=localhost:9092
gf.kafka.topic=t212_gf
gf.kafka.group-id=gf-data-processor
gf.kafka.max-poll-records=5000
gf.kafka.fetch-max-bytes=52428800
gf.kafka.auto-offset-reset=latest

# PostgreSQL配置
gf.postgres.url=****************************************
gf.postgres.username=flink_user
gf.postgres.password=password
gf.postgres.batch-size=2000
gf.postgres.batch-interval-ms=3000
gf.postgres.max-retries=3
gf.postgres.connection-timeout=30000

# 分区管理配置
gf.partition.cache-size=1000
gf.partition.cache-expire-hours=1
gf.partition.check-interval-seconds=300

# 监控配置
gf.metrics.enabled=true
gf.metrics.prometheus-port=9249
gf.metrics.report-interval-seconds=30
```

#### 4.4.2 日志配置 (log4j2.properties)

```properties
# 根Logger配置
log4j.rootLogger = INFO, console, debugFile, errorFile
#log4j.rootLogger =  INFO

#log4j.appender.console=org.apache.log4j.ConsoleAppender
#log4j.appender.console.layout = org.apache.log4j.PatternLayout
#log4j.appender.console.layout.ConversionPattern = [%-5p] %d(%r) --> [%t] %l: %m %x %n

log4j.appender.debugFile = org.apache.log4j.DailyRollingFileAppender
log4j.appender.debugFile.File = src/logs/debug.log
log4j.appender.debugFile.Append = true
log4j.appender.debugFile.Threshold = debug
log4j.appender.debugFile.layout = org.apache.log4j.PatternLayout
log4j.appender.debugFile.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n

log4j.appender.errorFile = org.apache.log4j.DailyRollingFileAppender
log4j.appender.errorFile.File = src/logs/error.log
log4j.appender.errorFile.Append = true
log4j.appender.errorFile.Threshold = error
log4j.appender.errorFile.layout = org.apache.log4j.PatternLayout
log4j.appender.errorFile.layout.ConversionPattern = %-d{yyyy-MM-dd HH:mm:ss}  [ %t:%r ] - [ %p ]  %m%n
```


### 4.2 数据库写入优化

#### 4.2.1 批量写入配置
```properties
# JDBC Sink配置
jdbc.batch.size: 2000
jdbc.batch.interval.ms: 3000
jdbc.max.retries: 3
jdbc.connection.max-pool-size: 15
```

#### 4.2.2 连接池优化
```properties
# HikariCP配置
hikari.minimum-idle: 5
hikari.maximum-pool-size: 15
hikari.connection-timeout: 30000
hikari.idle-timeout: 600000
hikari.max-lifetime: 1800000
```

#### 4.2.3 异步写入策略
- 使用AsyncIO降低写入延迟
- 并发请求数限制为10
- 实现自动重试和降级机制

## 6. 容错和监控设计

### 5.1 容错机制

#### 5.1.1 数据处理容错
```
异常类型              处理策略
JSON解析异常         记录错误日志，跳过当前记录
字段转换异常         使用默认值，标记数据异常  
时间格式异常         记录错误，使用当前时间
分区创建失败         重试3次，失败后告警
```

#### 5.1.2 数据库写入容错
```
异常类型              处理策略
连接超时             自动重连，指数退避重试
主键冲突             使用UPSERT策略
事务失败             回滚后重试，最多3次
分区不存在           自动创建分区后重试
```

## FAQ